#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JIDA PROJECT: Simple Web Interface for Delivery Time Prediction
Using basic Python libraries to create a simple web interface
"""

import pickle
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import os

class DeliveryPredictionHandler(BaseHTTPRequestHandler):
    
    def __init__(self, *args, **kwargs):
        # Load the model when the handler is created
        self.load_model()
        super().__init__(*args, **kwargs)
    
    def load_model(self):
        """Load the trained model"""
        try:
            with open('models/basic_delivery_model.pkl', 'rb') as f:
                model_data = pickle.load(f)
                self.model_data = model_data['model_data']
                self.sample_data = model_data['data_sample']
            print("✅ Model loaded successfully!")
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            self.model_data = None
            self.sample_data = []
    
    def predict_delivery_time(self, distance, package_size, weather, traffic, vehicle, hour):
        """Predict delivery time using the loaded model"""
        if not self.model_data:
            return 30.0  # Default prediction
        
        # Start with distance-based time
        predicted_time = distance * self.model_data['distance_factor']
        
        # Apply factors based on model data
        size_factor = self.model_data['avg_by_size'].get(package_size, self.model_data['base_time']) / self.model_data['base_time']
        weather_factor = self.model_data['avg_by_weather'].get(weather, self.model_data['base_time']) / self.model_data['base_time']
        traffic_factor = self.model_data['avg_by_traffic'].get(traffic, self.model_data['base_time']) / self.model_data['base_time']
        vehicle_factor = self.model_data['avg_by_vehicle'].get(vehicle, self.model_data['base_time']) / self.model_data['base_time']
        hour_factor = self.model_data['avg_by_hour'].get(hour, self.model_data['base_time']) / self.model_data['base_time']
        
        # Apply all factors
        predicted_time *= size_factor * weather_factor * traffic_factor * vehicle_factor * hour_factor
        
        return max(10, predicted_time)
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_html()
        elif self.path == '/predict':
            self.serve_prediction_form()
        else:
            self.send_error(404)
    
    def do_POST(self):
        """Handle POST requests"""
        if self.path == '/predict':
            self.handle_prediction()
        else:
            self.send_error(404)
    
    def serve_html(self):
        """Serve the main HTML page"""
        html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIDA - Delivery Time Predictor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #34495e;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background-color: #2980b9;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f5e8;
            border-radius: 5px;
            border-left: 4px solid #27ae60;
        }
        .error {
            background-color: #ffeaea;
            border-left: 4px solid #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 JIDA Delivery Time Predictor</h1>
        <p style="text-align: center; color: #7f8c8d;">Predict delivery times for last-mile logistics in Nigeria</p>
        
        <form method="POST" action="/predict">
            <div class="form-group">
                <label for="distance">Distance (km):</label>
                <input type="number" id="distance" name="distance" step="0.1" min="0.1" max="100" value="5.0" required>
            </div>
            
            <div class="form-group">
                <label for="package_size">Package Size:</label>
                <select id="package_size" name="package_size" required>
                    <option value="Small">Small</option>
                    <option value="Medium" selected>Medium</option>
                    <option value="Large">Large</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="weather">Weather Condition:</label>
                <select id="weather" name="weather" required>
                    <option value="Clear" selected>Clear</option>
                    <option value="Cloudy">Cloudy</option>
                    <option value="Rain">Rain</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="traffic">Traffic Level:</label>
                <select id="traffic" name="traffic" required>
                    <option value="Low">Low</option>
                    <option value="Medium" selected>Medium</option>
                    <option value="High">High</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="vehicle">Vehicle Type:</label>
                <select id="vehicle" name="vehicle" required>
                    <option value="Bike">Bike</option>
                    <option value="Van" selected>Van</option>
                    <option value="Truck">Truck</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="hour">Hour of Day (0-23):</label>
                <input type="number" id="hour" name="hour" min="0" max="23" value="14" required>
            </div>
            
            <button type="submit">🔮 Predict Delivery Time</button>
        </form>
    </div>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def handle_prediction(self):
        """Handle prediction request"""
        try:
            # Parse form data
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            form_data = parse_qs(post_data)
            
            # Extract parameters
            distance = float(form_data['distance'][0])
            package_size = form_data['package_size'][0]
            weather = form_data['weather'][0]
            traffic = form_data['traffic'][0]
            vehicle = form_data['vehicle'][0]
            hour = int(form_data['hour'][0])
            
            # Make prediction
            predicted_time = self.predict_delivery_time(
                distance, package_size, weather, traffic, vehicle, hour
            )
            
            # Calculate estimated cost (simple calculation)
            if vehicle == 'Truck':
                fuel_cost = distance * 4.0
            elif vehicle == 'Van':
                fuel_cost = distance * 2.0
            else:  # Bike
                fuel_cost = distance * 0.5
            
            # Generate response HTML
            result_html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIDA - Prediction Result</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
        }}
        .result {{
            background-color: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #27ae60;
            margin: 20px 0;
        }}
        .prediction {{
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
            text-align: center;
            margin: 20px 0;
        }}
        .details {{
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }}
        .back-button {{
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 20px;
        }}
        .back-button:hover {{
            background-color: #2980b9;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Prediction Result</h1>
        
        <div class="result">
            <div class="prediction">
                ⏱️ Estimated Delivery Time: {predicted_time:.1f} minutes
            </div>
            <div style="text-align: center; color: #7f8c8d;">
                💰 Estimated Fuel Cost: ₦{fuel_cost:.2f}
            </div>
        </div>
        
        <div class="details">
            <h3>📋 Input Details:</h3>
            <ul>
                <li><strong>Distance:</strong> {distance} km</li>
                <li><strong>Package Size:</strong> {package_size}</li>
                <li><strong>Weather:</strong> {weather}</li>
                <li><strong>Traffic Level:</strong> {traffic}</li>
                <li><strong>Vehicle Type:</strong> {vehicle}</li>
                <li><strong>Hour of Day:</strong> {hour}:00</li>
            </ul>
        </div>
        
        <div style="text-align: center;">
            <a href="/" class="back-button">🔄 Make Another Prediction</a>
        </div>
    </div>
</body>
</html>
            """
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(result_html.encode())
            
        except Exception as e:
            error_html = f"""
<!DOCTYPE html>
<html>
<head><title>Error</title></head>
<body>
    <h1>Error</h1>
    <p>An error occurred: {str(e)}</p>
    <a href="/">Go back</a>
</body>
</html>
            """
            self.send_response(500)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(error_html.encode())

def run_server(port=8000):
    """Run the web server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, DeliveryPredictionHandler)
    print(f"🌐 Starting JIDA Delivery Predictor server on port {port}")
    print(f"🔗 Open your browser and go to: http://localhost:{port}")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped!")
        httpd.server_close()

if __name__ == "__main__":
    # Check if model exists
    if not os.path.exists('models/basic_delivery_model.pkl'):
        print("❌ Model file not found! Please run jida_basic.py first to train the model.")
        exit(1)
    
    run_server()
