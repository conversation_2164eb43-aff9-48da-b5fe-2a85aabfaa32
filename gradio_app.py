#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JIDA PROJECT: Simple Web Interface for Delivery Time Prediction
Using basic Python libraries to create a simple web interface
"""

import pickle
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import os

class DeliveryPredictionHandler(BaseHTTPRequestHandler):
    
    def __init__(self, *args, **kwargs):
        # Load the model when the handler is created
        self.load_model()
        super().__init__(*args, **kwargs)
    
    def load_model(self):
        """Load the trained model"""
        try:
            with open('models/basic_delivery_model.pkl', 'rb') as f:
                model_data = pickle.load(f)
                self.model_data = model_data['model_data']
                self.sample_data = model_data['data_sample']
            print("✅ Model loaded successfully!")
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            self.model_data = None
            self.sample_data = []
    
    def predict_delivery_time(self, distance, package_size, weather, traffic, vehicle, hour):
        """Predict delivery time using the loaded model"""
        if not self.model_data:
            return 30.0  # Default prediction
        
        # Start with distance-based time
        predicted_time = distance * self.model_data['distance_factor']
        
        # Apply factors based on model data
        size_factor = self.model_data['avg_by_size'].get(package_size, self.model_data['base_time']) / self.model_data['base_time']
        weather_factor = self.model_data['avg_by_weather'].get(weather, self.model_data['base_time']) / self.model_data['base_time']
        traffic_factor = self.model_data['avg_by_traffic'].get(traffic, self.model_data['base_time']) / self.model_data['base_time']
        vehicle_factor = self.model_data['avg_by_vehicle'].get(vehicle, self.model_data['base_time']) / self.model_data['base_time']
        hour_factor = self.model_data['avg_by_hour'].get(hour, self.model_data['base_time']) / self.model_data['base_time']
        
        # Apply all factors
        predicted_time *= size_factor * weather_factor * traffic_factor * vehicle_factor * hour_factor
        
        return max(10, predicted_time)
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_html()
        elif self.path == '/predict':
            self.serve_prediction_form()
        else:
            self.send_error(404)
    
    def do_POST(self):
        """Handle POST requests"""
        if self.path == '/predict':
            self.handle_prediction()
        else:
            self.send_error(404)
    
    def serve_html(self):
        """Serve the main HTML page"""
        html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIDA - AI-Powered Delivery Predictor</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .card-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        @media (max-width: 768px) {
            .card-container {
                grid-template-columns: 1fr;
            }
            .header h1 {
                font-size: 2rem;
            }
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 600px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #34495e;
            font-size: 0.95rem;
        }

        .input-wrapper {
            position: relative;
        }

        input, select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e8ed;
            border-radius: 12px;
            font-size: 16px;
            font-family: inherit;
            transition: all 0.3s ease;
            background: white;
        }

        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .predict-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .predict-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .predict-button:active {
            transform: translateY(0);
        }

        .stats-card {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            border-top: 3px solid #667eea;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .feature-highlight {
            background: rgba(102, 126, 234, 0.1);
            border-left: 4px solid #667eea;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .feature-highlight h4 {
            color: #667eea;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1><i class="fas fa-shipping-fast"></i> JIDA</h1>
            <div class="subtitle">AI-Powered Last-Mile Delivery Intelligence for Nigeria</div>
        </div>

        <div class="card-container">
            <!-- Prediction Form Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <div class="card-title">Delivery Time Predictor</div>
                </div>

                <form method="POST" action="/predict" id="predictionForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="distance" class="tooltip" data-tooltip="Distance from depot to delivery location">
                                <i class="fas fa-route"></i> Distance (km)
                            </label>
                            <div class="input-wrapper">
                                <input type="number" id="distance" name="distance" step="0.1" min="0.1" max="100" value="5.0" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="package_size">
                                <i class="fas fa-box"></i> Package Size
                            </label>
                            <select id="package_size" name="package_size" required>
                                <option value="Small">📦 Small (< 2kg)</option>
                                <option value="Medium" selected>📦 Medium (2-10kg)</option>
                                <option value="Large">📦 Large (> 10kg)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="weather">
                                <i class="fas fa-cloud-sun"></i> Weather Condition
                            </label>
                            <select id="weather" name="weather" required>
                                <option value="Clear" selected>☀️ Clear</option>
                                <option value="Cloudy">☁️ Cloudy</option>
                                <option value="Rain">🌧️ Rain</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="traffic">
                                <i class="fas fa-traffic-light"></i> Traffic Level
                            </label>
                            <select id="traffic" name="traffic" required>
                                <option value="Low">🟢 Low</option>
                                <option value="Medium" selected>🟡 Medium</option>
                                <option value="High">🔴 High</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="vehicle">
                                <i class="fas fa-truck"></i> Vehicle Type
                            </label>
                            <select id="vehicle" name="vehicle" required>
                                <option value="Bike">🏍️ Motorcycle</option>
                                <option value="Van" selected>🚐 Van</option>
                                <option value="Truck">🚛 Truck</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="hour" class="tooltip" data-tooltip="24-hour format (0-23)">
                                <i class="fas fa-clock"></i> Hour of Day
                            </label>
                            <input type="number" id="hour" name="hour" min="0" max="23" value="14" required>
                        </div>
                    </div>

                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <div>Calculating optimal delivery time...</div>
                    </div>

                    <button type="submit" class="predict-button">
                        <i class="fas fa-magic"></i> Predict Delivery Time
                    </button>
                </form>
            </div>

            <!-- Information Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="card-title">How It Works</div>
                </div>

                <div class="feature-highlight">
                    <h4><i class="fas fa-brain"></i> AI-Powered Predictions</h4>
                    <p>Our machine learning model analyzes multiple factors to provide accurate delivery time estimates.</p>
                </div>

                <div class="feature-highlight">
                    <h4><i class="fas fa-map-marked-alt"></i> Nigerian Context</h4>
                    <p>Specifically trained on Nigerian logistics data including Lagos traffic patterns and local conditions.</p>
                </div>

                <div class="feature-highlight">
                    <h4><i class="fas fa-chart-line"></i> Real-time Factors</h4>
                    <p>Considers weather, traffic, vehicle type, package size, and time of day for precise estimates.</p>
                </div>

                <div class="feature-highlight">
                    <h4><i class="fas fa-money-bill-wave"></i> Cost Estimation</h4>
                    <p>Get fuel cost estimates along with delivery time predictions for better planning.</p>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="card stats-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="card-title">Model Performance</div>
                </div>

                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number">1,000+</div>
                        <div class="stat-label">Training Records</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">30.1</div>
                        <div class="stat-label">Avg Time (min)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6.95</div>
                        <div class="stat-label">Avg Distance (km)</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">₦9.92</div>
                        <div class="stat-label">Avg Fuel Cost</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('predictionForm').addEventListener('submit', function() {
            document.getElementById('loading').style.display = 'block';
        });

        // Add smooth animations
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def handle_prediction(self):
        """Handle prediction request"""
        try:
            # Parse form data
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            form_data = parse_qs(post_data)
            
            # Extract parameters
            distance = float(form_data['distance'][0])
            package_size = form_data['package_size'][0]
            weather = form_data['weather'][0]
            traffic = form_data['traffic'][0]
            vehicle = form_data['vehicle'][0]
            hour = int(form_data['hour'][0])
            
            # Make prediction
            predicted_time = self.predict_delivery_time(
                distance, package_size, weather, traffic, vehicle, hour
            )
            
            # Calculate estimated cost (simple calculation)
            if vehicle == 'Truck':
                fuel_cost = distance * 4.0
            elif vehicle == 'Van':
                fuel_cost = distance * 2.0
            else:  # Bike
                fuel_cost = distance * 0.5
            
            # Generate response HTML
            result_html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIDA - Prediction Result</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }}

        .main-container {{
            max-width: 900px;
            margin: 0 auto;
        }}

        .header {{
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }}

        .header h1 {{
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }}

        .success-icon {{
            font-size: 4rem;
            color: #27ae60;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }}

        @keyframes bounce {{
            0%, 20%, 50%, 80%, 100% {{
                transform: translateY(0);
            }}
            40% {{
                transform: translateY(-10px);
            }}
            60% {{
                transform: translateY(-5px);
            }}
        }}

        .result-card {{
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            margin-bottom: 30px;
            text-align: center;
        }}

        .prediction-result {{
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }}

        .prediction-result::before {{
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }}

        @keyframes shine {{
            0% {{
                transform: translateX(-100%) translateY(-100%) rotate(45deg);
            }}
            50% {{
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }}
            100% {{
                transform: translateX(100%) translateY(100%) rotate(45deg);
            }}
        }}

        .prediction-time {{
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }}

        .prediction-label {{
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }}

        .cost-estimate {{
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            font-size: 1.3rem;
            font-weight: 600;
        }}

        .details-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}

        .detail-item {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }}

        .detail-label {{
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        .detail-value {{
            font-size: 1.1rem;
            color: #2c3e50;
            font-weight: 500;
        }}

        .action-buttons {{
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }}

        .btn {{
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }}

        .btn-primary {{
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }}

        .btn-secondary {{
            background: rgba(255,255,255,0.9);
            color: #667eea;
            border: 2px solid #667eea;
        }}

        .btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }}

        .insights {{
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }}

        .insights h3 {{
            color: #667eea;
            margin-bottom: 15px;
            font-weight: 600;
        }}

        .insight-item {{
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.7);
            border-radius: 8px;
            font-size: 0.95rem;
        }}

        @media (max-width: 768px) {{
            .action-buttons {{
                flex-direction: column;
            }}
            .prediction-time {{
                font-size: 2rem;
            }}
            .header h1 {{
                font-size: 2rem;
            }}
        }}
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <div class="success-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h1><i class="fas fa-shipping-fast"></i> Prediction Complete</h1>
        </div>

        <div class="result-card">
            <div class="prediction-result">
                <div class="prediction-time">
                    <i class="fas fa-clock"></i> {predicted_time:.1f} min
                </div>
                <div class="prediction-label">
                    Estimated Delivery Time
                </div>
            </div>

            <div class="cost-estimate">
                <i class="fas fa-gas-pump"></i> Estimated Fuel Cost: ₦{fuel_cost:.2f}
            </div>

            <div class="details-grid">
                <div class="detail-item">
                    <div class="detail-label">
                        <i class="fas fa-route"></i> Distance
                    </div>
                    <div class="detail-value">{distance} km</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="fas fa-box"></i> Package Size
                    </div>
                    <div class="detail-value">{package_size}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="fas fa-cloud-sun"></i> Weather
                    </div>
                    <div class="detail-value">{weather}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="fas fa-traffic-light"></i> Traffic
                    </div>
                    <div class="detail-value">{traffic}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="fas fa-truck"></i> Vehicle
                    </div>
                    <div class="detail-value">{vehicle}</div>
                </div>

                <div class="detail-item">
                    <div class="detail-label">
                        <i class="fas fa-clock"></i> Time
                    </div>
                    <div class="detail-value">{hour}:00</div>
                </div>
            </div>

            <div class="insights">
                <h3><i class="fas fa-lightbulb"></i> AI Insights</h3>
                <div class="insight-item">
                    <strong>Speed Estimate:</strong> {(distance / (predicted_time / 60)):.1f} km/h average delivery speed
                </div>
                <div class="insight-item">
                    <strong>Cost Efficiency:</strong> ₦{(fuel_cost / distance):.2f} per kilometer
                </div>
                <div class="insight-item">
                    <strong>Time Efficiency:</strong> {(predicted_time / distance):.1f} minutes per kilometer
                </div>
            </div>

            <div class="action-buttons">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New Prediction
                </a>
                <button onclick="window.print()" class="btn btn-secondary">
                    <i class="fas fa-print"></i> Print Result
                </button>
            </div>
        </div>
    </div>

    <script>
        // Add entrance animation
        document.addEventListener('DOMContentLoaded', function() {{
            const card = document.querySelector('.result-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            setTimeout(() => {{
                card.style.transition = 'all 0.8s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }}, 100);
        }});
    </script>
</body>
</html>
            """
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(result_html.encode())
            
        except Exception as e:
            error_html = f"""
<!DOCTYPE html>
<html>
<head><title>Error</title></head>
<body>
    <h1>Error</h1>
    <p>An error occurred: {str(e)}</p>
    <a href="/">Go back</a>
</body>
</html>
            """
            self.send_response(500)
            self.send_header('Content-type', 'text/html')
            self.end_headers()
            self.wfile.write(error_html.encode())

def run_server(port=8000):
    """Run the web server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, DeliveryPredictionHandler)
    print(f"🌐 Starting JIDA Delivery Predictor server on port {port}")
    print(f"🔗 Open your browser and go to: http://localhost:{port}")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped!")
        httpd.server_close()

if __name__ == "__main__":
    # Check if model exists
    if not os.path.exists('models/basic_delivery_model.pkl'):
        print("❌ Model file not found! Please run jida_basic.py first to train the model.")
        exit(1)
    
    run_server()
