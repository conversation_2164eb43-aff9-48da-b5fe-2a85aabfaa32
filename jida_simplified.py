# -*- coding: utf-8 -*-
"""
JIDA PROJECT: Intelligent Last-Mile Delivery Optimization
Simplified version for model training and Gradio interface
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Machine Learning Libraries
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.cluster import KMeans
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression

# Deep Learning Libraries
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping

# Interface Library
import gradio as gr

# Utility Libraries
import pickle
import joblib
import os

# Set random seeds for reproducibility
np.random.seed(42)
tf.random.set_seed(42)

print("🚀 JIDA PROJECT: Intelligent Last-Mile Delivery Optimization")
print("=" * 60)
print("All libraries imported successfully!")
print("TensorFlow version:", tf.__version__)
print("=" * 60)

class IntelligentLastMileOptimizer:
    """
    Advanced Last-Mile Delivery Optimization System
    """

    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.lstm_model = None
        self.baseline_models = {}
        self.data = None
        self.X_scaled = None
        self.y = None
        self.feature_names = []
        self.model_performance = {}

        print("🤖 Intelligent Last-Mile Optimizer initialized!")
        print("Ready to optimize delivery operations...")

    def generate_dummy_data(self, n_samples=10000):
        """Generate comprehensive dummy delivery data"""
        print(f"\n📊 Generating {n_samples:,} delivery records...")
        np.random.seed(42)

        # Nigerian delivery companies
        companies = ['GIG Logistics', 'Kwik Delivery', 'MAX.ng', 'Jumia Logistics', 'ACE Logistics']
        
        # Generate base data
        data = {
            'order_id': [f"ORD_{i:06d}" for i in range(1, n_samples + 1)],
            'company': np.random.choice(companies, n_samples),
            'delivery_date': pd.date_range('2023-01-01', periods=n_samples, freq='30min'),
        }

        # Generate geographical coordinates (Lagos, Nigeria focus)
        latitudes = 6.5244 + np.random.normal(0, 0.05, n_samples)
        longitudes = 3.3792 + np.random.normal(0, 0.05, n_samples)
        
        data['latitude'] = latitudes
        data['longitude'] = longitudes

        # Generate package characteristics
        data['package_weight'] = np.random.lognormal(1.5, 0.8, n_samples)
        data['package_size'] = np.random.choice(['Small', 'Medium', 'Large'], n_samples, p=[0.5, 0.35, 0.15])
        data['package_value'] = np.random.exponential(100, n_samples)

        # Generate delivery characteristics
        data['vehicle_type'] = np.random.choice(['Bike', 'Van', 'Truck'], n_samples, p=[0.6, 0.3, 0.1])
        data['weather_condition'] = np.random.choice(['Clear', 'Rain', 'Cloudy'], n_samples, p=[0.6, 0.2, 0.2])
        data['traffic_level'] = np.random.choice(['Low', 'Medium', 'High'], n_samples, p=[0.3, 0.4, 0.3])

        # Calculate distances
        depot_lat, depot_lon = 6.5244, 3.3792
        distances = []
        for lat, lon in zip(latitudes, longitudes):
            dist = np.sqrt((lat - depot_lat)**2 + (lon - depot_lon)**2) * 111
            distances.append(max(1, dist + np.random.normal(0, 2)))

        data['distance_km'] = distances

        # Generate time-based features
        df_temp = pd.DataFrame(data)
        df_temp['hour'] = df_temp['delivery_date'].dt.hour
        df_temp['day_of_week'] = df_temp['delivery_date'].dt.dayofweek
        df_temp['month'] = df_temp['delivery_date'].dt.month
        df_temp['is_weekend'] = (df_temp['day_of_week'] >= 5).astype(int)
        df_temp['is_peak_hour'] = df_temp['hour'].isin([8, 9, 17, 18, 19]).astype(int)

        # Generate realistic delivery times
        delivery_times = []
        fuel_costs = []

        for i in range(n_samples):
            base_time = df_temp.iloc[i]['distance_km'] * 2.5

            # Apply various factors
            if df_temp.iloc[i]['package_size'] == 'Large':
                base_time *= 1.3
            elif df_temp.iloc[i]['package_size'] == 'Medium':
                base_time *= 1.1

            if df_temp.iloc[i]['weather_condition'] == 'Rain':
                base_time *= 1.4
            elif df_temp.iloc[i]['weather_condition'] == 'Cloudy':
                base_time *= 1.1

            if df_temp.iloc[i]['traffic_level'] == 'High':
                base_time *= 1.5
            elif df_temp.iloc[i]['traffic_level'] == 'Medium':
                base_time *= 1.2

            if df_temp.iloc[i]['is_peak_hour']:
                base_time *= 1.3

            if df_temp.iloc[i]['vehicle_type'] == 'Truck':
                base_time *= 1.2
            elif df_temp.iloc[i]['vehicle_type'] == 'Van':
                base_time *= 1.1

            final_time = max(10, base_time + np.random.normal(0, 5))
            delivery_times.append(final_time)

            # Calculate fuel cost
            if df_temp.iloc[i]['vehicle_type'] == 'Truck':
                fuel_cost = df_temp.iloc[i]['distance_km'] * np.random.uniform(3, 5)
            elif df_temp.iloc[i]['vehicle_type'] == 'Van':
                fuel_cost = df_temp.iloc[i]['distance_km'] * np.random.uniform(1.5, 3)
            else:  # Bike
                fuel_cost = df_temp.iloc[i]['distance_km'] * np.random.uniform(0.3, 1)

            fuel_costs.append(fuel_cost)

        data['delivery_time_minutes'] = delivery_times
        data['fuel_cost'] = fuel_costs

        # Create final DataFrame
        self.data = pd.DataFrame(data)
        self.data['hour'] = self.data['delivery_date'].dt.hour
        self.data['day_of_week'] = self.data['delivery_date'].dt.dayofweek
        self.data['month'] = self.data['delivery_date'].dt.month
        self.data['is_weekend'] = (self.data['day_of_week'] >= 5).astype(int)
        self.data['is_peak_hour'] = self.data['hour'].isin([8, 9, 17, 18, 19]).astype(int)
        self.data['cost_per_km'] = self.data['fuel_cost'] / self.data['distance_km']
        self.data['time_per_km'] = self.data['delivery_time_minutes'] / self.data['distance_km']
        self.data['speed_kmh'] = self.data['distance_km'] / (self.data['delivery_time_minutes'] / 60)

        print(f"✅ Generated {len(self.data):,} delivery records successfully!")
        print(f"📈 Data shape: {self.data.shape}")
        
        return self.data

    def preprocess_data(self):
        """Preprocess data for machine learning"""
        print("\n🔧 Preprocessing data for ML...")

        # Encode categorical variables
        categorical_cols = ['company', 'package_size', 'vehicle_type', 'weather_condition', 'traffic_level']
        
        for col in categorical_cols:
            le = LabelEncoder()
            self.data[f'{col}_encoded'] = le.fit_transform(self.data[col])
            self.label_encoders[col] = le

        # Select features
        self.feature_names = [
            'latitude', 'longitude', 'package_weight', 'distance_km',
            'hour', 'day_of_week', 'month', 'is_weekend', 'is_peak_hour',
            'company_encoded', 'package_size_encoded', 'vehicle_type_encoded',
            'weather_condition_encoded', 'traffic_level_encoded'
        ]

        # Prepare feature matrix and target
        self.X = self.data[self.feature_names].copy()
        self.y = self.data['delivery_time_minutes'].copy()

        # Handle missing values
        self.X = self.X.fillna(self.X.mean())

        # Scale features
        self.X_scaled = self.scaler.fit_transform(self.X)

        print(f"✅ Data preprocessing completed!")
        print(f"📊 Feature matrix shape: {self.X_scaled.shape}")
        print(f"🎯 Target shape: {self.y.shape}")

        return self.X_scaled, self.y

    def train_baseline_models(self):
        """Train baseline models"""
        print("\n🤖 Training baseline models...")

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            self.X_scaled, self.y, test_size=0.2, random_state=42
        )

        # Define models
        models = {
            'Linear Regression': LinearRegression(),
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'Gradient Boosting': GradientBoostingRegressor(n_estimators=100, random_state=42)
        }

        # Train and evaluate models
        for name, model in models.items():
            print(f"  Training {name}...")
            
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            
            mse = mean_squared_error(y_test, y_pred)
            mae = mean_absolute_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            self.baseline_models[name] = model
            self.model_performance[name] = {
                'MSE': mse,
                'MAE': mae,
                'R2': r2,
                'RMSE': np.sqrt(mse)
            }
            
            print(f"    {name} - RMSE: {np.sqrt(mse):.2f}, MAE: {mae:.2f}, R²: {r2:.3f}")

        print("✅ Baseline models training completed!")
        return X_train, X_test, y_train, y_test

    def create_sequences(self, X, y, sequence_length=10):
        """Create sequences for LSTM model"""
        sequences_X, sequences_y = [], []

        for i in range(len(X) - sequence_length):
            sequences_X.append(X[i:i+sequence_length])
            sequences_y.append(y[i+sequence_length])

        return np.array(sequences_X), np.array(sequences_y)

    def build_lstm_model(self, input_shape):
        """Build LSTM neural network model"""
        print("🧠 Building LSTM model...")

        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            LSTM(50, return_sequences=True),
            Dropout(0.2),
            LSTM(50),
            Dropout(0.2),
            Dense(25, activation='relu'),
            Dense(1)
        ])

        model.compile(optimizer=Adam(learning_rate=0.001),
                     loss='mse',
                     metrics=['mae'])

        print("LSTM Model Architecture:")
        model.summary()

        return model

    def train_lstm_model(self, sequence_length=10):
        """Train the LSTM model"""
        print("\n🧠 Training LSTM model...")

        # Create sequences
        X_seq, y_seq = self.create_sequences(self.X_scaled, self.y.values, sequence_length)

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_seq, y_seq, test_size=0.3, random_state=42
        )

        print(f"Training sequences: {X_train.shape}")
        print(f"Testing sequences: {X_test.shape}")

        # Build model
        self.lstm_model = self.build_lstm_model((sequence_length, X_train.shape[2]))

        # Early stopping
        early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)

        # Train model
        history = self.lstm_model.fit(
            X_train, y_train,
            batch_size=32,
            epochs=50,
            validation_split=0.2,
            callbacks=[early_stopping],
            verbose=1
        )

        # Evaluate model
        y_pred = self.lstm_model.predict(X_test)

        # Calculate metrics
        rmse = np.sqrt(mean_squared_error(y_test, y_pred))
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)

        self.model_performance['LSTM'] = {
            'MSE': mean_squared_error(y_test, y_pred),
            'MAE': mae,
            'R2': r2,
            'RMSE': rmse
        }

        print(f"\nLSTM Model Performance:")
        print(f"RMSE: {rmse:.4f}")
        print(f"MAE: {mae:.4f}")
        print(f"R²: {r2:.4f}")

        return X_test, y_test, y_pred

    def export_model(self):
        """Export the trained model and preprocessing components"""
        print("\n💾 Exporting trained models...")

        # Create models directory
        os.makedirs('models', exist_ok=True)

        # Save LSTM model
        if self.lstm_model is not None:
            model_path = 'models/lstm_delivery_model.h5'
            self.lstm_model.save(model_path)
            print(f"✅ LSTM model saved to {model_path}")

        # Save best baseline model (Random Forest)
        if 'Random Forest' in self.baseline_models:
            rf_path = 'models/random_forest_model.pkl'
            joblib.dump(self.baseline_models['Random Forest'], rf_path)
            print(f"✅ Random Forest model saved to {rf_path}")

        # Save scaler
        scaler_path = 'models/scaler.pkl'
        joblib.dump(self.scaler, scaler_path)
        print(f"✅ Scaler saved to {scaler_path}")

        # Save label encoders
        encoders_path = 'models/label_encoders.pkl'
        with open(encoders_path, 'wb') as f:
            pickle.dump(self.label_encoders, f)
        print(f"✅ Label encoders saved to {encoders_path}")

        # Save feature names
        features_path = 'models/feature_names.pkl'
        with open(features_path, 'wb') as f:
            pickle.dump(self.feature_names, f)
        print(f"✅ Feature names saved to {features_path}")

        # Save model performance
        performance_path = 'models/model_performance.pkl'
        with open(performance_path, 'wb') as f:
            pickle.dump(self.model_performance, f)
        print(f"✅ Model performance saved to {performance_path}")

        print("🎉 Model export completed!")

if __name__ == "__main__":
    # Initialize optimizer
    optimizer = IntelligentLastMileOptimizer()

    # Generate data
    data = optimizer.generate_dummy_data(10000)

    # Preprocess data
    X_scaled, y = optimizer.preprocess_data()

    # Train baseline models
    X_train, X_test, y_train, y_test = optimizer.train_baseline_models()

    # Train LSTM model
    X_test_lstm, y_test_lstm, y_pred_lstm = optimizer.train_lstm_model()

    # Export models
    optimizer.export_model()

    print("\n🎉 Training and export completed!")
    print("Models saved in 'models/' directory")
