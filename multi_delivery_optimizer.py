# -*- coding: utf-8 -*-
"""
JIDA PROJECT: Multi-Delivery Route Optimization System
Handles multiple deliveries and merges them into optimal single routes
"""

import pandas as pd
import numpy as np
import pickle
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Optimization libraries (using basic implementations)
import itertools
import math

# Set random seed for reproducibility
np.random.seed(42)

class MultiDeliveryOptimizer:
    """
    Advanced Multi-Delivery Route Optimization System
    Combines multiple deliveries into optimal single routes
    """

    def __init__(self):
        self.deliveries = []
        self.optimized_routes = []
        self.depot_location = {'lat': 6.5244, 'lon': 3.3792}  # Lagos, Nigeria
        self.vehicle_capacity = {'Bike': 50, 'Van': 500, 'Truck': 2000}  # kg
        self.vehicle_speed = {'Bike': 25, 'Van': 35, 'Truck': 30}  # km/h
        self.optimization_results = {}
        
        # Load the basic prediction model
        try:
            with open('models/basic_delivery_model.pkl', 'rb') as f:
                model_data = pickle.load(f)
                self.prediction_model = model_data['model_data']
        except:
            self.prediction_model = None
            print("⚠️ Basic model not found. Using default parameters.")

        print("🚀 Multi-Delivery Optimizer initialized!")

    def add_delivery(self, delivery_data):
        """Add a single delivery to the optimization queue"""
        required_fields = ['id', 'lat', 'lon', 'package_weight', 'package_size', 
                          'priority', 'time_window_start', 'time_window_end']
        
        # Validate delivery data
        for field in required_fields:
            if field not in delivery_data:
                raise ValueError(f"Missing required field: {field}")
        
        # Add default values
        delivery_data.setdefault('weather', 'Clear')
        delivery_data.setdefault('traffic', 'Medium')
        delivery_data.setdefault('vehicle_preference', 'Any')
        delivery_data.setdefault('special_instructions', '')
        
        self.deliveries.append(delivery_data)
        print(f"✅ Added delivery {delivery_data['id']} to optimization queue")

    def add_multiple_deliveries(self, deliveries_list):
        """Add multiple deliveries at once"""
        for delivery in deliveries_list:
            self.add_delivery(delivery)
        print(f"✅ Added {len(deliveries_list)} deliveries to optimization queue")

    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """Calculate distance between two points using Haversine formula"""
        # Simplified distance calculation (for demo purposes)
        return np.sqrt((lat1 - lat2)**2 + (lon1 - lon2)**2) * 111  # Approximate km

    def predict_delivery_time(self, distance, package_size, weather, traffic, vehicle, hour):
        """Predict delivery time for a single delivery"""
        if not self.prediction_model:
            # Fallback calculation
            base_time = distance * 2.5
            if package_size == 'Large': base_time *= 1.3
            elif package_size == 'Medium': base_time *= 1.1
            if weather == 'Rain': base_time *= 1.4
            if traffic == 'High': base_time *= 1.5
            elif traffic == 'Medium': base_time *= 1.2
            return max(10, base_time)
        
        # Use trained model
        base_time = distance * self.prediction_model['distance_factor']
        
        size_factor = self.prediction_model['avg_by_size'].get(package_size, self.prediction_model['base_time']) / self.prediction_model['base_time']
        weather_factor = self.prediction_model['avg_by_weather'].get(weather, self.prediction_model['base_time']) / self.prediction_model['base_time']
        traffic_factor = self.prediction_model['avg_by_traffic'].get(traffic, self.prediction_model['base_time']) / self.prediction_model['base_time']
        vehicle_factor = self.prediction_model['avg_by_vehicle'].get(vehicle, self.prediction_model['base_time']) / self.prediction_model['base_time']
        hour_factor = self.prediction_model['avg_by_hour'].get(hour, self.prediction_model['base_time']) / self.prediction_model['base_time']
        
        predicted_time = base_time * size_factor * weather_factor * traffic_factor * vehicle_factor * hour_factor
        return max(10, predicted_time)

    def cluster_deliveries_by_location(self, max_clusters=10):
        """Cluster deliveries by geographical location using simple k-means"""
        if len(self.deliveries) < 2:
            return [list(range(len(self.deliveries)))]

        if len(self.deliveries) <= 3:
            return [list(range(len(self.deliveries)))]

        # Simple clustering based on distance
        n_clusters = min(max_clusters, max(2, len(self.deliveries) // 3))

        # Extract coordinates
        coords = [[d['lat'], d['lon']] for d in self.deliveries]

        # Simple k-means implementation
        clusters = self.simple_kmeans(coords, n_clusters)

        return clusters

    def simple_kmeans(self, points, k, max_iterations=100):
        """Simple k-means clustering implementation"""
        if len(points) <= k:
            return [[i] for i in range(len(points))]

        # Initialize centroids randomly
        centroids = []
        for i in range(k):
            idx = i * len(points) // k
            centroids.append(points[idx][:])

        for iteration in range(max_iterations):
            # Assign points to nearest centroid
            clusters = [[] for _ in range(k)]

            for i, point in enumerate(points):
                min_dist = float('inf')
                closest_centroid = 0

                for j, centroid in enumerate(centroids):
                    dist = self.calculate_distance(point[0], point[1], centroid[0], centroid[1])
                    if dist < min_dist:
                        min_dist = dist
                        closest_centroid = j

                clusters[closest_centroid].append(i)

            # Update centroids
            new_centroids = []
            for cluster in clusters:
                if cluster:
                    avg_lat = sum(points[i][0] for i in cluster) / len(cluster)
                    avg_lon = sum(points[i][1] for i in cluster) / len(cluster)
                    new_centroids.append([avg_lat, avg_lon])
                else:
                    new_centroids.append(centroids[len(new_centroids)][:])

            # Check for convergence
            converged = True
            for i in range(k):
                if abs(new_centroids[i][0] - centroids[i][0]) > 0.0001 or \
                   abs(new_centroids[i][1] - centroids[i][1]) > 0.0001:
                    converged = False
                    break

            centroids = new_centroids

            if converged:
                break

        # Remove empty clusters
        return [cluster for cluster in clusters if cluster]

    def solve_tsp_for_cluster(self, delivery_indices):
        """Solve TSP for a cluster of deliveries"""
        if len(delivery_indices) <= 1:
            return delivery_indices
        
        # Create distance matrix including depot
        locations = [self.depot_location] + [self.deliveries[i] for i in delivery_indices]
        n = len(locations)
        
        # Calculate distance matrix
        distances = np.zeros((n, n))
        for i in range(n):
            for j in range(n):
                if i != j:
                    lat1 = locations[i]['lat'] if 'lat' in locations[i] else locations[i]['lat']
                    lon1 = locations[i]['lon'] if 'lon' in locations[i] else locations[i]['lon']
                    lat2 = locations[j]['lat'] if 'lat' in locations[j] else locations[j]['lat']
                    lon2 = locations[j]['lon'] if 'lon' in locations[j] else locations[j]['lon']
                    distances[i][j] = self.calculate_distance(lat1, lon1, lat2, lon2)
        
        # For small clusters, use brute force
        if len(delivery_indices) <= 6:
            best_route = None
            best_distance = float('inf')
            
            for perm in itertools.permutations(range(1, n)):  # Start from depot (0)
                route = [0] + list(perm) + [0]  # Return to depot
                total_distance = sum(distances[route[i]][route[i+1]] for i in range(len(route)-1))
                
                if total_distance < best_distance:
                    best_distance = total_distance
                    best_route = route
            
            # Convert back to delivery indices (exclude depot)
            return [delivery_indices[i-1] for i in best_route[1:-1]]
        
        # For larger clusters, use nearest neighbor heuristic
        else:
            unvisited = set(range(1, n))
            current = 0  # Start at depot
            route = [current]
            
            while unvisited:
                nearest = min(unvisited, key=lambda x: distances[current][x])
                route.append(nearest)
                unvisited.remove(nearest)
                current = nearest
            
            # Convert back to delivery indices (exclude depot)
            return [delivery_indices[i-1] for i in route[1:]]

    def optimize_vehicle_assignment(self, route_deliveries):
        """Optimize vehicle assignment for a route"""
        total_weight = sum(self.deliveries[i]['package_weight'] for i in route_deliveries)
        
        # Find the most suitable vehicle
        suitable_vehicles = []
        for vehicle, capacity in self.vehicle_capacity.items():
            if total_weight <= capacity:
                suitable_vehicles.append(vehicle)
        
        if not suitable_vehicles:
            return 'Truck'  # Default to largest vehicle
        
        # Choose most efficient vehicle (considering speed and capacity utilization)
        best_vehicle = suitable_vehicles[0]
        best_efficiency = 0
        
        for vehicle in suitable_vehicles:
            capacity_utilization = total_weight / self.vehicle_capacity[vehicle]
            speed_factor = self.vehicle_speed[vehicle] / 35  # Normalize to van speed
            efficiency = capacity_utilization * speed_factor
            
            if efficiency > best_efficiency:
                best_efficiency = efficiency
                best_vehicle = vehicle
        
        return best_vehicle

    def create_optimized_routes(self):
        """Create optimized routes from all deliveries"""
        if not self.deliveries:
            print("❌ No deliveries to optimize")
            return []
        
        print(f"\n🔄 Optimizing {len(self.deliveries)} deliveries...")
        
        # Step 1: Cluster deliveries by location
        clusters = self.cluster_deliveries_by_location()
        print(f"📍 Created {len(clusters)} geographical clusters")
        
        # Step 2: Optimize each cluster
        optimized_routes = []
        total_distance = 0
        total_time = 0
        total_cost = 0
        
        for cluster_idx, cluster in enumerate(clusters):
            print(f"  Optimizing cluster {cluster_idx + 1} with {len(cluster)} deliveries...")
            
            # Solve TSP for this cluster
            optimized_order = self.solve_tsp_for_cluster(cluster)
            
            # Assign optimal vehicle
            optimal_vehicle = self.optimize_vehicle_assignment(optimized_order)
            
            # Calculate route metrics
            route_distance = 0
            route_time = 0
            route_deliveries = []
            
            # Add depot to start
            current_lat, current_lon = self.depot_location['lat'], self.depot_location['lon']
            
            for delivery_idx in optimized_order:
                delivery = self.deliveries[delivery_idx]
                
                # Calculate distance from current location
                distance = self.calculate_distance(current_lat, current_lon, delivery['lat'], delivery['lon'])
                route_distance += distance
                
                # Predict delivery time
                delivery_time = self.predict_delivery_time(
                    distance, delivery['package_size'], delivery['weather'],
                    delivery['traffic'], optimal_vehicle, 14  # Default hour
                )
                route_time += delivery_time
                
                route_deliveries.append({
                    'delivery_id': delivery['id'],
                    'sequence': len(route_deliveries) + 1,
                    'distance_from_previous': distance,
                    'estimated_time': delivery_time,
                    'package_weight': delivery['package_weight'],
                    'priority': delivery['priority']
                })
                
                # Update current location
                current_lat, current_lon = delivery['lat'], delivery['lon']
            
            # Return to depot
            return_distance = self.calculate_distance(current_lat, current_lon, 
                                                    self.depot_location['lat'], self.depot_location['lon'])
            route_distance += return_distance
            
            # Calculate route cost
            if optimal_vehicle == 'Truck':
                route_cost = route_distance * 4.0
            elif optimal_vehicle == 'Van':
                route_cost = route_distance * 2.0
            else:  # Bike
                route_cost = route_distance * 0.5
            
            route_info = {
                'route_id': f"ROUTE_{cluster_idx + 1:03d}",
                'vehicle_type': optimal_vehicle,
                'total_deliveries': len(optimized_order),
                'total_distance_km': round(route_distance, 2),
                'total_time_minutes': round(route_time, 2),
                'total_cost_naira': round(route_cost, 2),
                'total_weight_kg': sum(self.deliveries[i]['package_weight'] for i in optimized_order),
                'deliveries': route_deliveries,
                'efficiency_score': round((len(optimized_order) / route_time) * 100, 2)
            }
            
            optimized_routes.append(route_info)
            total_distance += route_distance
            total_time += route_time
            total_cost += route_cost
        
        # Store results
        self.optimized_routes = optimized_routes
        self.optimization_results = {
            'total_routes': len(optimized_routes),
            'total_deliveries': len(self.deliveries),
            'total_distance_km': round(total_distance, 2),
            'total_time_minutes': round(total_time, 2),
            'total_cost_naira': round(total_cost, 2),
            'average_deliveries_per_route': round(len(self.deliveries) / len(optimized_routes), 2),
            'optimization_timestamp': datetime.now().isoformat()
        }
        
        print(f"✅ Optimization complete!")
        print(f"📊 Created {len(optimized_routes)} optimized routes")
        print(f"🚚 Total distance: {total_distance:.2f} km")
        print(f"⏱️ Total time: {total_time:.2f} minutes")
        print(f"💰 Total cost: ₦{total_cost:.2f}")
        
        return optimized_routes

    def get_optimization_summary(self):
        """Get a summary of the optimization results"""
        if not self.optimized_routes:
            return "No optimization results available. Run create_optimized_routes() first."
        
        summary = {
            'overview': self.optimization_results,
            'route_details': []
        }
        
        for route in self.optimized_routes:
            route_summary = {
                'route_id': route['route_id'],
                'vehicle': route['vehicle_type'],
                'deliveries': route['total_deliveries'],
                'distance': f"{route['total_distance_km']} km",
                'time': f"{route['total_time_minutes']:.1f} min",
                'cost': f"₦{route['total_cost_naira']:.2f}",
                'efficiency': f"{route['efficiency_score']:.1f}/100"
            }
            summary['route_details'].append(route_summary)
        
        return summary

    def export_routes(self, filename='optimized_routes.json'):
        """Export optimized routes to JSON file"""
        if not self.optimized_routes:
            print("❌ No routes to export. Run optimization first.")
            return
        
        export_data = {
            'optimization_results': self.optimization_results,
            'routes': self.optimized_routes,
            'original_deliveries': self.deliveries
        }
        
        with open(filename, 'w') as f:
            json.dump(export_data, f, indent=2, default=str)
        
        print(f"✅ Routes exported to {filename}")

def create_sample_deliveries():
    """Create sample deliveries for testing"""
    sample_deliveries = []
    
    # Lagos coordinates with some variation
    base_lat, base_lon = 6.5244, 3.3792
    
    for i in range(15):
        delivery = {
            'id': f"DEL_{i+1:03d}",
            'lat': base_lat + np.random.normal(0, 0.02),
            'lon': base_lon + np.random.normal(0, 0.02),
            'package_weight': np.random.uniform(1, 25),
            'package_size': np.random.choice(['Small', 'Medium', 'Large'], p=[0.5, 0.3, 0.2]),
            'priority': np.random.choice(['Low', 'Medium', 'High'], p=[0.3, 0.5, 0.2]),
            'time_window_start': f"{np.random.randint(8, 12)}:00",
            'time_window_end': f"{np.random.randint(15, 20)}:00",
            'weather': np.random.choice(['Clear', 'Cloudy', 'Rain'], p=[0.6, 0.3, 0.1]),
            'traffic': np.random.choice(['Low', 'Medium', 'High'], p=[0.3, 0.4, 0.3])
        }
        sample_deliveries.append(delivery)
    
    return sample_deliveries

if __name__ == "__main__":
    # Demo the multi-delivery optimization
    print("🚀 JIDA Multi-Delivery Optimization Demo")
    print("=" * 50)
    
    # Initialize optimizer
    optimizer = MultiDeliveryOptimizer()
    
    # Create sample deliveries
    sample_deliveries = create_sample_deliveries()
    
    # Add deliveries to optimizer
    optimizer.add_multiple_deliveries(sample_deliveries)
    
    # Create optimized routes
    routes = optimizer.create_optimized_routes()
    
    # Get summary
    summary = optimizer.get_optimization_summary()
    
    # Export results
    optimizer.export_routes()
    
    print("\n📋 Optimization Summary:")
    print(f"Total Routes: {summary['overview']['total_routes']}")
    print(f"Total Distance: {summary['overview']['total_distance_km']} km")
    print(f"Total Time: {summary['overview']['total_time_minutes']:.1f} minutes")
    print(f"Total Cost: ₦{summary['overview']['total_cost_naira']:.2f}")
    
    print("\n🎉 Multi-delivery optimization complete!")
