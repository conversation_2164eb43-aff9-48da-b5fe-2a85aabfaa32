#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JIDA PROJECT: Multi-Delivery Route Optimization Web Interface
Web application for optimizing multiple deliveries into single routes
"""

import json
import pickle
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import os
from multi_delivery_optimizer import MultiDeliveryOptimizer

# Global optimizer instance to persist data between requests
global_optimizer = MultiDeliveryOptimizer()

class MultiDeliveryHandler(BaseHTTPRequestHandler):

    def __init__(self, *args, **kwargs):
        # Use the global optimizer instance
        self.optimizer = global_optimizer
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_main_page()
        elif self.path == '/optimize':
            self.serve_optimization_page()
        elif self.path == '/results':
            self.serve_results_page()
        elif self.path == '/status':
            self.serve_status()
        else:
            self.send_error(404)
    
    def do_POST(self):
        """Handle POST requests"""
        if self.path == '/add_delivery':
            self.handle_add_delivery()
        elif self.path == '/optimize_routes':
            self.handle_optimize_routes()
        elif self.path == '/clear_deliveries':
            self.handle_clear_deliveries()
        else:
            self.send_error(404)
    
    def serve_main_page(self):
        """Serve the main page for adding deliveries"""
        html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIDA - Multi-Delivery Route Optimizer</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        
        .main-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .nav-menu {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .nav-button {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
        }

        .nav-button:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .nav-button.active {
            background: rgba(255,255,255,0.9);
            color: #667eea;
            border-color: rgba(255,255,255,0.9);
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        @media (max-width: 1200px) {
            .dashboard {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
        }
        
        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 1.5rem;
        }
        
        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #34495e;
            font-size: 0.9rem;
        }
        
        input, select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            font-family: inherit;
            transition: all 0.3s ease;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        .delivery-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }
        
        .delivery-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .delivery-id {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .delivery-details {
            font-size: 0.9rem;
            color: #7f8c8d;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-buttons .btn {
            flex: 1;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid;
        }
        
        .alert-success {
            background: rgba(39, 174, 96, 0.1);
            border-color: #27ae60;
            color: #27ae60;
        }
        
        .alert-warning {
            background: rgba(243, 156, 18, 0.1);
            border-color: #f39c12;
            color: #f39c12;
        }

        .alert-info {
            background: rgba(52, 152, 219, 0.1);
            border-color: #3498db;
            color: #3498db;
        }

        .alert-error {
            background: rgba(231, 76, 60, 0.1);
            border-color: #e74c3c;
            color: #e74c3c;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1><i class="fas fa-route"></i> JIDA Multi-Delivery Optimizer</h1>
            <div class="subtitle">Optimize Multiple Deliveries into Single Efficient Routes</div>

            <div class="nav-menu">
                <a href="http://localhost:8000" target="_blank" class="nav-button">
                    <i class="fas fa-calculator"></i> Single Delivery
                </a>
                <a href="#" class="nav-button active">
                    <i class="fas fa-route"></i> Multi-Delivery
                </a>
            </div>
        </div>
        
        <div class="dashboard">
            <!-- Add Delivery Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="card-title">Add Delivery</div>
                </div>
                
                <form id="deliveryForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="delivery_id">Delivery ID</label>
                            <input type="text" id="delivery_id" name="delivery_id" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="priority">Priority</label>
                            <select id="priority" name="priority" required>
                                <option value="Low">🟢 Low</option>
                                <option value="Medium" selected>🟡 Medium</option>
                                <option value="High">🔴 High</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="latitude">Latitude</label>
                            <input type="number" id="latitude" name="latitude" step="0.0001" value="6.5244" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="longitude">Longitude</label>
                            <input type="number" id="longitude" name="longitude" step="0.0001" value="3.3792" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="package_weight">Weight (kg)</label>
                            <input type="number" id="package_weight" name="package_weight" step="0.1" min="0.1" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="package_size">Package Size</label>
                            <select id="package_size" name="package_size" required>
                                <option value="Small">📦 Small</option>
                                <option value="Medium" selected>📦 Medium</option>
                                <option value="Large">📦 Large</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="time_start">Time Window Start</label>
                            <input type="time" id="time_start" name="time_start" value="09:00" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="time_end">Time Window End</label>
                            <input type="time" id="time_end" name="time_end" value="17:00" required>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Delivery
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Current Deliveries Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="card-title">Current Deliveries</div>
                </div>
                
                <div id="deliveryList" class="delivery-list">
                    <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                        <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 10px;"></i>
                        <div>No deliveries added yet</div>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button onclick="clearDeliveries()" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Clear All
                    </button>
                </div>
            </div>
            
            <!-- Optimization Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-magic"></i>
                    </div>
                    <div class="card-title">Route Optimization</div>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-number" id="totalDeliveries">0</div>
                        <div class="stat-label">Deliveries</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="totalWeight">0</div>
                        <div class="stat-label">Total KG</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number" id="highPriority">0</div>
                        <div class="stat-label">High Priority</div>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button onclick="optimizeRoutes()" class="btn btn-success" id="optimizeBtn" disabled>
                        <i class="fas fa-route"></i> Optimize Routes
                    </button>
                </div>
                
                <div id="optimizationResult" style="margin-top: 20px;"></div>
            </div>
        </div>
    </div>
    
    <script>
        let deliveries = [];
        
        document.getElementById('deliveryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            addDelivery();
        });
        
        function addDelivery() {
            const formData = new FormData(document.getElementById('deliveryForm'));
            const delivery = {
                id: formData.get('delivery_id'),
                lat: parseFloat(formData.get('latitude')),
                lon: parseFloat(formData.get('longitude')),
                package_weight: parseFloat(formData.get('package_weight')),
                package_size: formData.get('package_size'),
                priority: formData.get('priority'),
                time_window_start: formData.get('time_start'),
                time_window_end: formData.get('time_end')
            };

            console.log('Adding delivery:', delivery);

            // Send to server
            fetch('/add_delivery', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(delivery)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Server response:', data);
                if (data.success) {
                    deliveries.push(delivery);
                    updateDeliveryList();
                    updateStats();
                    document.getElementById('deliveryForm').reset();

                    // Generate new ID
                    document.getElementById('delivery_id').value = 'DEL_' + String(deliveries.length + 1).padStart(3, '0');

                    // Show success message
                    showMessage(`✅ Delivery ${delivery.id} added successfully! Total: ${data.total_deliveries}`, 'success');
                } else {
                    showMessage(`❌ Error: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage(`❌ Network error: ${error.message}`, 'error');
            });
        }
        
        function updateDeliveryList() {
            const listElement = document.getElementById('deliveryList');
            
            if (deliveries.length === 0) {
                listElement.innerHTML = `
                    <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                        <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 10px;"></i>
                        <div>No deliveries added yet</div>
                    </div>
                `;
                return;
            }
            
            listElement.innerHTML = deliveries.map(delivery => `
                <div class="delivery-item">
                    <div class="delivery-id">${delivery.id}</div>
                    <div class="delivery-details">
                        📍 ${delivery.lat.toFixed(4)}, ${delivery.lon.toFixed(4)} | 
                        📦 ${delivery.package_weight}kg (${delivery.package_size}) | 
                        🚨 ${delivery.priority} Priority
                    </div>
                </div>
            `).join('');
        }
        
        function updateStats() {
            document.getElementById('totalDeliveries').textContent = deliveries.length;
            document.getElementById('totalWeight').textContent = deliveries.reduce((sum, d) => sum + d.package_weight, 0).toFixed(1);
            document.getElementById('highPriority').textContent = deliveries.filter(d => d.priority === 'High').length;
            
            document.getElementById('optimizeBtn').disabled = deliveries.length < 2;
        }
        
        function clearDeliveries() {
            if (deliveries.length === 0) {
                showMessage('ℹ️ No deliveries to clear', 'info');
                return;
            }

            if (confirm(`Are you sure you want to clear all ${deliveries.length} deliveries?`)) {
                fetch('/clear_deliveries', {method: 'POST'})
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        deliveries = [];
                        updateDeliveryList();
                        updateStats();
                        document.getElementById('optimizationResult').innerHTML = '';
                        showMessage('✅ All deliveries cleared successfully', 'success');
                    }
                })
                .catch(error => {
                    showMessage(`❌ Error clearing deliveries: ${error.message}`, 'error');
                });
            }
        }

        function showMessage(message, type = 'info') {
            // Remove existing messages
            const existingMessages = document.querySelectorAll('.message');
            existingMessages.forEach(msg => msg.remove());

            // Create message element
            const messageDiv = document.createElement('div');
            messageDiv.className = `message alert alert-${type}`;
            messageDiv.innerHTML = message;
            messageDiv.style.position = 'fixed';
            messageDiv.style.top = '20px';
            messageDiv.style.right = '20px';
            messageDiv.style.zIndex = '1000';
            messageDiv.style.maxWidth = '400px';
            messageDiv.style.animation = 'slideIn 0.3s ease';

            document.body.appendChild(messageDiv);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => messageDiv.remove(), 300);
                }
            }, 5000);
        }
        
        function optimizeRoutes() {
            console.log('Starting optimization...');
            document.getElementById('optimizeBtn').disabled = true;
            document.getElementById('optimizeBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Optimizing...';

            fetch('/optimize_routes', {method: 'POST'})
            .then(response => response.json())
            .then(data => {
                console.log('Optimization response:', data);
                if (data.success) {
                    displayOptimizationResults(data.results);
                    showMessage('✅ Route optimization completed successfully!', 'success');
                } else {
                    showMessage(`❌ Optimization failed: ${data.error}`, 'error');
                }
                document.getElementById('optimizeBtn').disabled = false;
                document.getElementById('optimizeBtn').innerHTML = '<i class="fas fa-route"></i> Optimize Routes';
            })
            .catch(error => {
                console.error('Optimization error:', error);
                showMessage(`❌ Network error: ${error.message}`, 'error');
                document.getElementById('optimizeBtn').disabled = false;
                document.getElementById('optimizeBtn').innerHTML = '<i class="fas fa-route"></i> Optimize Routes';
            });
        }
        
        function displayOptimizationResults(results) {
            const resultElement = document.getElementById('optimizationResult');
            resultElement.innerHTML = `
                <div class="alert alert-success">
                    <strong>✅ Optimization Complete!</strong><br>
                    🚚 ${results.total_routes} routes created<br>
                    📏 ${results.total_distance_km} km total distance<br>
                    ⏱️ ${results.total_time_minutes.toFixed(1)} minutes total time<br>
                    💰 ₦${results.total_cost_naira.toFixed(2)} total cost
                </div>
                <button onclick="window.open('/results', '_blank')" class="btn btn-primary" style="width: 100%; margin-top: 10px;">
                    <i class="fas fa-eye"></i> View Detailed Results
                </button>
            `;
        }
        
        // Initialize
        document.getElementById('delivery_id').value = 'DEL_001';
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def handle_add_delivery(self):
        """Handle adding a delivery"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length).decode('utf-8')
            delivery_data = json.loads(post_data)

            print(f"📦 Adding delivery: {delivery_data['id']}")
            self.optimizer.add_delivery(delivery_data)

            print(f"📊 Total deliveries now: {len(self.optimizer.deliveries)}")

            response = {
                'success': True,
                'message': 'Delivery added successfully',
                'total_deliveries': len(self.optimizer.deliveries)
            }

        except Exception as e:
            print(f"❌ Error adding delivery: {e}")
            response = {'success': False, 'error': str(e)}

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def handle_optimize_routes(self):
        """Handle route optimization"""
        try:
            print(f"🔄 Starting optimization with {len(self.optimizer.deliveries)} deliveries")

            if len(self.optimizer.deliveries) == 0:
                response = {'success': False, 'error': 'No deliveries to optimize. Please add deliveries first.'}
            else:
                routes = self.optimizer.create_optimized_routes()
                results = self.optimizer.optimization_results

                print(f"✅ Optimization complete: {len(routes)} routes created")
                response = {'success': True, 'results': results, 'routes': routes}

        except Exception as e:
            print(f"❌ Optimization error: {e}")
            import traceback
            traceback.print_exc()
            response = {'success': False, 'error': str(e)}

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())
    
    def handle_clear_deliveries(self):
        """Handle clearing all deliveries"""
        print(f"🗑️ Clearing {len(self.optimizer.deliveries)} deliveries")

        self.optimizer.deliveries = []
        self.optimizer.optimized_routes = []

        print("✅ All deliveries cleared")
        response = {'success': True, 'message': 'All deliveries cleared'}

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(response).encode())

    def serve_status(self):
        """Serve current status of deliveries"""
        status = {
            'total_deliveries': len(self.optimizer.deliveries),
            'deliveries': [{'id': d['id'], 'priority': d['priority']} for d in self.optimizer.deliveries],
            'has_routes': len(self.optimizer.optimized_routes) > 0,
            'total_routes': len(self.optimizer.optimized_routes)
        }

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(status).encode())

    def serve_results_page(self):
        """Serve the optimization results page"""
        if not self.optimizer.optimized_routes:
            # Redirect to main page if no results
            self.send_response(302)
            self.send_header('Location', '/')
            self.end_headers()
            return

        results = self.optimizer.optimization_results
        routes = self.optimizer.optimized_routes

        # Generate route cards HTML
        route_cards = ""
        for route in routes:
            deliveries_html = ""
            for delivery in route['deliveries']:
                deliveries_html += f"""
                    <div class="delivery-step">
                        <div class="step-number">{delivery['sequence']}</div>
                        <div class="step-details">
                            <div class="step-id">{delivery['delivery_id']}</div>
                            <div class="step-info">
                                📏 {delivery['distance_from_previous']:.1f}km |
                                ⏱️ {delivery['estimated_time']:.1f}min |
                                📦 {delivery['package_weight']}kg
                            </div>
                        </div>
                    </div>
                """

            route_cards += f"""
                <div class="route-card">
                    <div class="route-header">
                        <div class="route-title">
                            <i class="fas fa-route"></i> {route['route_id']}
                        </div>
                        <div class="route-vehicle">
                            <i class="fas fa-truck"></i> {route['vehicle_type']}
                        </div>
                    </div>

                    <div class="route-stats">
                        <div class="stat">
                            <div class="stat-value">{route['total_deliveries']}</div>
                            <div class="stat-label">Deliveries</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">{route['total_distance_km']}</div>
                            <div class="stat-label">KM</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">{route['total_time_minutes']:.0f}</div>
                            <div class="stat-label">Minutes</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">₦{route['total_cost_naira']:.0f}</div>
                            <div class="stat-label">Cost</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">{route['efficiency_score']:.0f}</div>
                            <div class="stat-label">Efficiency</div>
                        </div>
                    </div>

                    <div class="route-deliveries">
                        <h4><i class="fas fa-list-ol"></i> Delivery Sequence</h4>
                        <div class="delivery-steps">
                            <div class="delivery-step depot">
                                <div class="step-number">🏢</div>
                                <div class="step-details">
                                    <div class="step-id">DEPOT START</div>
                                    <div class="step-info">Lagos Distribution Center</div>
                                </div>
                            </div>
                            {deliveries_html}
                            <div class="delivery-step depot">
                                <div class="step-number">🏢</div>
                                <div class="step-details">
                                    <div class="step-id">DEPOT RETURN</div>
                                    <div class="step-info">Return to Distribution Center</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            """

        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIDA - Optimization Results</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }}

        .main-container {{
            max-width: 1400px;
            margin: 0 auto;
        }}

        .header {{
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }}

        .header h1 {{
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }}

        .summary-card {{
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }}

        .summary-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}

        .summary-stat {{
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 12px;
        }}

        .summary-stat .value {{
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }}

        .summary-stat .label {{
            font-size: 0.9rem;
            opacity: 0.9;
        }}

        .routes-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
        }}

        .route-card {{
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }}

        .route-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f2f6;
        }}

        .route-title {{
            font-size: 1.3rem;
            font-weight: 600;
            color: #667eea;
        }}

        .route-vehicle {{
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }}

        .route-stats {{
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
            margin-bottom: 25px;
        }}

        .stat {{
            text-align: center;
            padding: 15px 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 10px;
        }}

        .stat-value {{
            font-size: 1.2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 5px;
        }}

        .stat-label {{
            font-size: 0.8rem;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }}

        .route-deliveries h4 {{
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }}

        .delivery-steps {{
            max-height: 300px;
            overflow-y: auto;
        }}

        .delivery-step {{
            display: flex;
            align-items: center;
            padding: 12px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }}

        .delivery-step.depot {{
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-left-color: #764ba2;
        }}

        .step-number {{
            width: 40px;
            height: 40px;
            background: #667eea;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 15px;
            font-size: 0.9rem;
        }}

        .delivery-step.depot .step-number {{
            background: #764ba2;
        }}

        .step-details {{
            flex: 1;
        }}

        .step-id {{
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 3px;
        }}

        .step-info {{
            font-size: 0.85rem;
            color: #7f8c8d;
        }}

        .action-buttons {{
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }}

        .btn {{
            padding: 15px 30px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }}

        .btn-primary {{
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }}

        .btn-success {{
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }}

        .btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }}

        @media (max-width: 768px) {{
            .routes-grid {{
                grid-template-columns: 1fr;
            }}
            .route-stats {{
                grid-template-columns: repeat(3, 1fr);
            }}
            .summary-stats {{
                grid-template-columns: repeat(2, 1fr);
            }}
        }}
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> Optimization Results</h1>
            <div class="subtitle">Multi-Delivery Route Optimization Complete</div>
        </div>

        <div class="summary-card">
            <h2 style="margin-bottom: 25px; color: #2c3e50;"><i class="fas fa-analytics"></i> Optimization Summary</h2>

            <div class="summary-stats">
                <div class="summary-stat">
                    <div class="value">{results['total_routes']}</div>
                    <div class="label">Optimized Routes</div>
                </div>
                <div class="summary-stat">
                    <div class="value">{results['total_deliveries']}</div>
                    <div class="label">Total Deliveries</div>
                </div>
                <div class="summary-stat">
                    <div class="value">{results['total_distance_km']}</div>
                    <div class="label">Total Distance (KM)</div>
                </div>
                <div class="summary-stat">
                    <div class="value">{results['total_time_minutes']:.0f}</div>
                    <div class="label">Total Time (Min)</div>
                </div>
                <div class="summary-stat">
                    <div class="value">₦{results['total_cost_naira']:.0f}</div>
                    <div class="label">Total Cost</div>
                </div>
                <div class="summary-stat">
                    <div class="value">{results['average_deliveries_per_route']:.1f}</div>
                    <div class="label">Avg Deliveries/Route</div>
                </div>
            </div>
        </div>

        <div class="routes-grid">
            {route_cards}
        </div>

        <div class="action-buttons">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add More Deliveries
            </a>
            <button onclick="window.print()" class="btn btn-success">
                <i class="fas fa-print"></i> Print Results
            </button>
        </div>
    </div>
</body>
</html>
        """

        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())

def run_server(port=8001):
    """Run the multi-delivery optimization server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MultiDeliveryHandler)
    print(f"🌐 Starting JIDA Multi-Delivery Optimizer on port {port}")
    print(f"🔗 Open your browser and go to: http://localhost:{port}")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped!")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
