#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for route optimization functionality
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the optimizer class
try:
    # Try to import from the original file
    exec(open('jida_project_jul_13_complete_ml_development.py').read())
    print("✅ Successfully imported IntelligentLastMileOptimizer")
except Exception as e:
    print(f"❌ Error importing optimizer: {e}")
    sys.exit(1)

def test_route_optimization():
    """Test the route optimization functionality"""
    print("🚀 Testing Route Optimization Functionality")
    print("=" * 60)
    
    try:
        # Initialize optimizer
        print("1. Initializing optimizer...")
        optimizer = IntelligentLastMileOptimizer()
        
        # Generate sample data
        print("2. Generating sample data...")
        data = optimizer.generate_comprehensive_dummy_data(n_samples=100)
        print(f"   Generated {len(data)} delivery records")
        
        # Test TSP optimization
        print("\n3. Testing TSP optimization...")
        try:
            tsp_results = optimizer.solve_tsp_optimization()
            print("   ✅ TSP optimization completed successfully")
            print(f"   📊 Results: {tsp_results}")
        except Exception as e:
            print(f"   ❌ TSP optimization failed: {e}")
        
        # Test clustering optimization
        print("\n4. Testing clustering optimization...")
        try:
            clustering_results = optimizer.clustering_based_optimization()
            print("   ✅ Clustering optimization completed successfully")
            print(f"   📊 Results: {clustering_results}")
        except Exception as e:
            print(f"   ❌ Clustering optimization failed: {e}")
        
        # Test multi-company optimization
        print("\n5. Testing multi-company optimization...")
        try:
            collab_results = optimizer.multi_company_optimization()
            print("   ✅ Multi-company optimization completed successfully")
            print(f"   📊 Results: {collab_results}")
        except Exception as e:
            print(f"   ❌ Multi-company optimization failed: {e}")
        
        # Test full route optimization
        print("\n6. Testing full route optimization...")
        try:
            full_results = optimizer.implement_route_optimization()
            print("   ✅ Full route optimization completed successfully")
            print(f"   📊 Results keys: {list(full_results.keys())}")
        except Exception as e:
            print(f"   ❌ Full route optimization failed: {e}")
        
        print("\n🎉 Route optimization testing completed!")
        return True
        
    except Exception as e:
        print(f"❌ Critical error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """Test basic functionality without route optimization"""
    print("\n🔧 Testing Basic Functionality")
    print("=" * 40)
    
    try:
        # Initialize optimizer
        optimizer = IntelligentLastMileOptimizer()
        
        # Generate data
        data = optimizer.generate_comprehensive_dummy_data(n_samples=50)
        print(f"✅ Generated {len(data)} records")
        
        # Test data analysis
        results = optimizer.analyze_delivery_inefficiencies()
        print("✅ Delivery analysis completed")
        
        # Test basic clustering (without route optimization)
        cluster_features = ['latitude', 'longitude']
        cluster_data = optimizer.data[cluster_features].copy()
        
        # Simple clustering
        coords = cluster_data.values
        n_clusters = 5
        
        # Basic k-means
        cluster_centers = []
        for i in range(n_clusters):
            idx = i * len(coords) // n_clusters
            cluster_centers.append(coords[idx])
        
        clusters = []
        for coord in coords:
            distances = [np.sqrt((coord[0] - center[0])**2 + (coord[1] - center[1])**2) 
                       for center in cluster_centers]
            clusters.append(np.argmin(distances))
        
        optimizer.data['simple_cluster'] = clusters
        
        # Analyze clusters
        cluster_analysis = optimizer.data.groupby('simple_cluster').agg({
            'order_id': 'count',
            'delivery_time_minutes': 'mean',
            'fuel_cost': 'mean',
            'distance_km': 'mean'
        }).round(2)
        
        print("✅ Basic clustering completed")
        print(f"📊 Cluster analysis:\n{cluster_analysis}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_route_optimizer():
    """Create a simple route optimizer that works without external dependencies"""
    print("\n🛠️ Creating Simple Route Optimizer")
    print("=" * 40)
    
    class SimpleRouteOptimizer:
        def __init__(self, deliveries):
            self.deliveries = deliveries
            self.depot = {'lat': 6.5244, 'lon': 3.3792}  # Lagos
        
        def calculate_distance(self, lat1, lon1, lat2, lon2):
            """Calculate simple distance between two points"""
            return np.sqrt((lat1 - lat2)**2 + (lon1 - lon2)**2) * 111  # Approximate km
        
        def nearest_neighbor_tsp(self, locations):
            """Simple nearest neighbor TSP solution"""
            if len(locations) <= 1:
                return locations
            
            unvisited = set(range(1, len(locations)))  # Skip depot (0)
            current = 0  # Start at depot
            route = [current]
            total_distance = 0
            
            while unvisited:
                nearest = min(unvisited, key=lambda x: self.calculate_distance(
                    locations[current]['lat'], locations[current]['lon'],
                    locations[x]['lat'], locations[x]['lon']
                ))
                
                distance = self.calculate_distance(
                    locations[current]['lat'], locations[current]['lon'],
                    locations[nearest]['lat'], locations[nearest]['lon']
                )
                
                total_distance += distance
                current = nearest
                route.append(current)
                unvisited.remove(current)
            
            # Return to depot
            return_distance = self.calculate_distance(
                locations[current]['lat'], locations[current]['lon'],
                locations[0]['lat'], locations[0]['lon']
            )
            total_distance += return_distance
            route.append(0)
            
            return route, total_distance
        
        def optimize_route(self):
            """Optimize delivery route"""
            # Prepare locations (depot + deliveries)
            locations = [self.depot] + self.deliveries
            
            # Solve TSP
            route, total_distance = self.nearest_neighbor_tsp(locations)
            
            # Calculate metrics
            total_time = total_distance * 2.5  # Assume 2.5 min per km
            total_cost = total_distance * 2.0  # Assume ₦2 per km
            
            return {
                'route': route,
                'total_distance': total_distance,
                'total_time': total_time,
                'total_cost': total_cost,
                'deliveries_count': len(self.deliveries)
            }
    
    # Test with sample deliveries
    sample_deliveries = [
        {'lat': 6.5244 + np.random.normal(0, 0.02), 'lon': 3.3792 + np.random.normal(0, 0.02)}
        for _ in range(8)
    ]
    
    optimizer = SimpleRouteOptimizer(sample_deliveries)
    result = optimizer.optimize_route()
    
    print(f"✅ Simple route optimization completed")
    print(f"📊 Route: {result['route']}")
    print(f"📏 Distance: {result['total_distance']:.2f} km")
    print(f"⏱️ Time: {result['total_time']:.2f} minutes")
    print(f"💰 Cost: ₦{result['total_cost']:.2f}")
    
    return True

if __name__ == "__main__":
    print("🧪 JIDA Route Optimization Test Suite")
    print("=" * 60)
    
    # Test 1: Basic functionality
    basic_success = test_basic_functionality()
    
    # Test 2: Simple route optimizer
    simple_success = create_simple_route_optimizer()
    
    # Test 3: Full route optimization (if possible)
    if basic_success:
        route_success = test_route_optimization()
    else:
        route_success = False
    
    # Summary
    print("\n📋 TEST SUMMARY")
    print("=" * 30)
    print(f"Basic functionality: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"Simple optimizer: {'✅ PASS' if simple_success else '❌ FAIL'}")
    print(f"Full route optimization: {'✅ PASS' if route_success else '❌ FAIL'}")
    
    if basic_success and simple_success:
        print("\n🎉 Core functionality is working!")
        print("💡 Route optimization is available through the multi-delivery system")
        print("🌐 Web interfaces:")
        print("   - Single delivery: http://localhost:8000")
        print("   - Multi-delivery: http://localhost:8001")
    else:
        print("\n⚠️ Some issues detected. Check error messages above.")
