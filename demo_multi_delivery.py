#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JIDA PROJECT: Multi-Delivery Optimization Demo
Demonstrates how to merge multiple deliveries into optimized single routes
"""

from multi_delivery_optimizer import MultiDeliveryOptimizer
import j<PERSON>

def demo_basic_optimization():
    """Demonstrate basic multi-delivery optimization"""
    print("🚀 JIDA Multi-Delivery Optimization Demo")
    print("=" * 60)
    
    # Initialize optimizer
    optimizer = MultiDeliveryOptimizer()
    
    # Create sample deliveries around Lagos
    sample_deliveries = [
        {
            'id': 'DEL_001',
            'lat': 6.5244,  # Lagos Island
            'lon': 3.3792,
            'package_weight': 5.2,
            'package_size': 'Medium',
            'priority': 'High',
            'time_window_start': '09:00',
            'time_window_end': '17:00'
        },
        {
            'id': 'DEL_002',
            'lat': 6.4698,  # Ikeja
            'lon': 3.3378,
            'package_weight': 2.1,
            'package_size': 'Small',
            'priority': 'Medium',
            'time_window_start': '10:00',
            'time_window_end': '18:00'
        },
        {
            'id': 'DEL_003',
            'lat': 6.5795,  # Victoria Island
            'lon': 3.3211,
            'package_weight': 8.7,
            'package_size': 'Large',
            'priority': 'High',
            'time_window_start': '08:00',
            'time_window_end': '16:00'
        },
        {
            'id': 'DEL_004',
            'lat': 6.4281,  # Surulere
            'lon': 3.3611,
            'package_weight': 3.4,
            'package_size': 'Medium',
            'priority': 'Low',
            'time_window_start': '11:00',
            'time_window_end': '19:00'
        },
        {
            'id': 'DEL_005',
            'lat': 6.6018,  # Ikoyi
            'lon': 3.3515,
            'package_weight': 1.8,
            'package_size': 'Small',
            'priority': 'Medium',
            'time_window_start': '09:30',
            'time_window_end': '17:30'
        },
        {
            'id': 'DEL_006',
            'lat': 6.4474,  # Yaba
            'lon': 3.3903,
            'package_weight': 6.3,
            'package_size': 'Medium',
            'priority': 'High',
            'time_window_start': '08:30',
            'time_window_end': '16:30'
        },
        {
            'id': 'DEL_007',
            'lat': 6.5244,  # Lagos Mainland
            'lon': 3.3792,
            'package_weight': 4.1,
            'package_size': 'Medium',
            'priority': 'Medium',
            'time_window_start': '10:30',
            'time_window_end': '18:30'
        },
        {
            'id': 'DEL_008',
            'lat': 6.4969,  # Apapa
            'lon': 3.3560,
            'package_weight': 12.5,
            'package_size': 'Large',
            'priority': 'High',
            'time_window_start': '07:00',
            'time_window_end': '15:00'
        }
    ]
    
    print(f"\n📦 Adding {len(sample_deliveries)} deliveries to optimization queue...")
    
    # Add deliveries to optimizer
    for delivery in sample_deliveries:
        optimizer.add_delivery(delivery)
    
    print(f"\n🔍 Analysis before optimization:")
    print(f"   Total deliveries: {len(optimizer.deliveries)}")
    print(f"   Total weight: {sum(d['package_weight'] for d in optimizer.deliveries):.1f} kg")
    print(f"   High priority: {len([d for d in optimizer.deliveries if d['priority'] == 'High'])}")
    print(f"   Medium priority: {len([d for d in optimizer.deliveries if d['priority'] == 'Medium'])}")
    print(f"   Low priority: {len([d for d in optimizer.deliveries if d['priority'] == 'Low'])}")
    
    # Create optimized routes
    print(f"\n🔄 Creating optimized routes...")
    routes = optimizer.create_optimized_routes()
    
    # Display results
    print(f"\n📊 OPTIMIZATION RESULTS:")
    print(f"=" * 40)
    
    results = optimizer.optimization_results
    print(f"🚚 Total routes created: {results['total_routes']}")
    print(f"📏 Total distance: {results['total_distance_km']} km")
    print(f"⏱️ Total time: {results['total_time_minutes']:.1f} minutes")
    print(f"💰 Total cost: ₦{results['total_cost_naira']:.2f}")
    print(f"📈 Average deliveries per route: {results['average_deliveries_per_route']:.1f}")
    
    print(f"\n🗺️ ROUTE DETAILS:")
    print(f"=" * 40)
    
    for i, route in enumerate(routes, 1):
        print(f"\n🚛 Route {i}: {route['route_id']}")
        print(f"   Vehicle: {route['vehicle_type']}")
        print(f"   Deliveries: {route['total_deliveries']}")
        print(f"   Distance: {route['total_distance_km']} km")
        print(f"   Time: {route['total_time_minutes']:.1f} minutes")
        print(f"   Cost: ₦{route['total_cost_naira']:.2f}")
        print(f"   Weight: {route['total_weight_kg']:.1f} kg")
        print(f"   Efficiency: {route['efficiency_score']:.1f}/100")
        
        print(f"   📋 Delivery sequence:")
        for j, delivery in enumerate(route['deliveries'], 1):
            print(f"      {j}. {delivery['delivery_id']} "
                  f"({delivery['distance_from_previous']:.1f}km, "
                  f"{delivery['estimated_time']:.1f}min)")
    
    # Calculate efficiency metrics
    print(f"\n📈 EFFICIENCY ANALYSIS:")
    print(f"=" * 40)
    
    # Compare with individual deliveries
    individual_distance = 0
    individual_time = 0
    individual_cost = 0
    
    for delivery in optimizer.deliveries:
        # Distance from depot to delivery and back
        dist_to_delivery = optimizer.calculate_distance(
            optimizer.depot_location['lat'], optimizer.depot_location['lon'],
            delivery['lat'], delivery['lon']
        )
        round_trip_distance = dist_to_delivery * 2
        individual_distance += round_trip_distance
        
        # Time for individual delivery
        delivery_time = optimizer.predict_delivery_time(
            dist_to_delivery, delivery['package_size'], 
            delivery.get('weather', 'Clear'), delivery.get('traffic', 'Medium'),
            'Van', 14
        )
        individual_time += delivery_time * 2  # Round trip
        
        # Cost for individual delivery
        individual_cost += round_trip_distance * 2.0  # Van cost
    
    distance_savings = individual_distance - results['total_distance_km']
    time_savings = individual_time - results['total_time_minutes']
    cost_savings = individual_cost - results['total_cost_naira']
    
    print(f"💡 Individual deliveries would require:")
    print(f"   Distance: {individual_distance:.1f} km")
    print(f"   Time: {individual_time:.1f} minutes")
    print(f"   Cost: ₦{individual_cost:.2f}")
    
    print(f"\n🎯 Optimization savings:")
    print(f"   Distance saved: {distance_savings:.1f} km ({distance_savings/individual_distance*100:.1f}%)")
    print(f"   Time saved: {time_savings:.1f} minutes ({time_savings/individual_time*100:.1f}%)")
    print(f"   Cost saved: ₦{cost_savings:.2f} ({cost_savings/individual_cost*100:.1f}%)")
    
    # Export results
    optimizer.export_routes('demo_optimized_routes.json')
    
    print(f"\n✅ Demo completed! Results exported to 'demo_optimized_routes.json'")
    print(f"🌐 You can also test the web interface at http://localhost:8001")
    
    return optimizer

def demo_large_scale_optimization():
    """Demonstrate large-scale optimization with many deliveries"""
    print(f"\n" + "="*60)
    print("🚀 LARGE-SCALE OPTIMIZATION DEMO")
    print("="*60)
    
    optimizer = MultiDeliveryOptimizer()
    
    # Generate 50 random deliveries around Lagos
    import numpy as np
    np.random.seed(42)
    
    base_lat, base_lon = 6.5244, 3.3792
    large_deliveries = []
    
    for i in range(50):
        delivery = {
            'id': f'BULK_{i+1:03d}',
            'lat': base_lat + np.random.normal(0, 0.05),
            'lon': base_lon + np.random.normal(0, 0.05),
            'package_weight': np.random.uniform(0.5, 20),
            'package_size': np.random.choice(['Small', 'Medium', 'Large'], p=[0.5, 0.3, 0.2]),
            'priority': np.random.choice(['Low', 'Medium', 'High'], p=[0.3, 0.5, 0.2]),
            'time_window_start': f"{np.random.randint(8, 12)}:00",
            'time_window_end': f"{np.random.randint(15, 20)}:00"
        }
        large_deliveries.append(delivery)
    
    # Add all deliveries
    optimizer.add_multiple_deliveries(large_deliveries)
    
    # Optimize
    routes = optimizer.create_optimized_routes()
    results = optimizer.optimization_results
    
    print(f"\n📊 LARGE-SCALE RESULTS:")
    print(f"🚚 Created {results['total_routes']} routes for {results['total_deliveries']} deliveries")
    print(f"📏 Total distance: {results['total_distance_km']} km")
    print(f"⏱️ Total time: {results['total_time_minutes']:.1f} minutes")
    print(f"💰 Total cost: ₦{results['total_cost_naira']:.2f}")
    print(f"📈 Average deliveries per route: {results['average_deliveries_per_route']:.1f}")
    
    # Vehicle distribution
    vehicle_count = {}
    for route in routes:
        vehicle = route['vehicle_type']
        vehicle_count[vehicle] = vehicle_count.get(vehicle, 0) + 1
    
    print(f"\n🚛 Vehicle distribution:")
    for vehicle, count in vehicle_count.items():
        print(f"   {vehicle}: {count} routes")
    
    return optimizer

if __name__ == "__main__":
    # Run basic demo
    basic_optimizer = demo_basic_optimization()
    
    # Run large-scale demo
    large_optimizer = demo_large_scale_optimization()
    
    print(f"\n🎉 All demos completed!")
    print(f"🌐 Visit http://localhost:8001 to try the interactive web interface")
