#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the multi-delivery optimizer fix
"""

import requests
import json
import time

def test_multi_delivery_api():
    """Test the multi-delivery API endpoints"""
    base_url = "http://localhost:8001"
    
    print("🧪 Testing Multi-Delivery API")
    print("=" * 40)
    
    # Test 1: Check initial status
    print("1. Checking initial status...")
    try:
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ Initial deliveries: {status['total_deliveries']}")
        else:
            print(f"   ❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Status check error: {e}")
        return False
    
    # Test 2: Add some deliveries
    print("\n2. Adding test deliveries...")
    test_deliveries = [
        {
            'id': 'TEST_001',
            'lat': 6.5244,
            'lon': 3.3792,
            'package_weight': 5.0,
            'package_size': 'Medium',
            'priority': 'High',
            'time_window_start': '09:00',
            'time_window_end': '17:00'
        },
        {
            'id': 'TEST_002',
            'lat': 6.4698,
            'lon': 3.3378,
            'package_weight': 3.0,
            'package_size': 'Small',
            'priority': 'Medium',
            'time_window_start': '10:00',
            'time_window_end': '18:00'
        },
        {
            'id': 'TEST_003',
            'lat': 6.5795,
            'lon': 3.3211,
            'package_weight': 8.0,
            'package_size': 'Large',
            'priority': 'High',
            'time_window_start': '08:00',
            'time_window_end': '16:00'
        }
    ]
    
    for i, delivery in enumerate(test_deliveries, 1):
        try:
            response = requests.post(
                f"{base_url}/add_delivery",
                headers={'Content-Type': 'application/json'},
                data=json.dumps(delivery)
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"   ✅ Added delivery {delivery['id']} (Total: {result.get('total_deliveries', 'unknown')})")
                else:
                    print(f"   ❌ Failed to add delivery {delivery['id']}: {result.get('error', 'unknown error')}")
                    return False
            else:
                print(f"   ❌ HTTP error adding delivery {delivery['id']}: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error adding delivery {delivery['id']}: {e}")
            return False
    
    # Test 3: Check status after adding deliveries
    print("\n3. Checking status after adding deliveries...")
    try:
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ Total deliveries: {status['total_deliveries']}")
            print(f"   📦 Delivery IDs: {[d['id'] for d in status['deliveries']]}")
            
            if status['total_deliveries'] != len(test_deliveries):
                print(f"   ⚠️ Expected {len(test_deliveries)} deliveries, got {status['total_deliveries']}")
        else:
            print(f"   ❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ Status check error: {e}")
        return False
    
    # Test 4: Optimize routes
    print("\n4. Testing route optimization...")
    try:
        response = requests.post(f"{base_url}/optimize_routes")
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                results = result['results']
                routes = result['routes']
                
                print(f"   ✅ Optimization successful!")
                print(f"   🚚 Routes created: {results['total_routes']}")
                print(f"   📏 Total distance: {results['total_distance_km']} km")
                print(f"   ⏱️ Total time: {results['total_time_minutes']:.1f} minutes")
                print(f"   💰 Total cost: ₦{results['total_cost_naira']:.2f}")
                
                for i, route in enumerate(routes, 1):
                    print(f"      Route {i}: {route['total_deliveries']} deliveries, "
                          f"{route['vehicle_type']}, {route['total_distance_km']}km")
                
            else:
                print(f"   ❌ Optimization failed: {result.get('error', 'unknown error')}")
                return False
        else:
            print(f"   ❌ HTTP error during optimization: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Optimization error: {e}")
        return False
    
    # Test 5: Clear deliveries
    print("\n5. Testing clear deliveries...")
    try:
        response = requests.post(f"{base_url}/clear_deliveries")
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"   ✅ Deliveries cleared successfully")
                
                # Verify they're actually cleared
                status_response = requests.get(f"{base_url}/status")
                if status_response.status_code == 200:
                    status = status_response.json()
                    if status['total_deliveries'] == 0:
                        print(f"   ✅ Confirmed: {status['total_deliveries']} deliveries remaining")
                    else:
                        print(f"   ⚠️ Warning: {status['total_deliveries']} deliveries still remain")
            else:
                print(f"   ❌ Clear failed: {result.get('error', 'unknown error')}")
                return False
        else:
            print(f"   ❌ HTTP error during clear: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Clear error: {e}")
        return False
    
    print(f"\n🎉 All tests passed! Multi-delivery optimizer is working correctly.")
    return True

if __name__ == "__main__":
    print("🔧 Multi-Delivery Optimizer Fix Verification")
    print("=" * 50)
    
    # Wait a moment for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    try:
        success = test_multi_delivery_api()
        
        if success:
            print(f"\n✅ VERIFICATION SUCCESSFUL!")
            print(f"🌐 The multi-delivery optimizer is now working correctly.")
            print(f"🔗 You can test it at: http://localhost:8001")
        else:
            print(f"\n❌ VERIFICATION FAILED!")
            print(f"⚠️ There may still be issues with the multi-delivery optimizer.")
            
    except Exception as e:
        print(f"\n💥 VERIFICATION ERROR: {e}")
        print(f"🔍 Make sure the server is running on port 8001")
    
    print(f"\n📋 Manual Testing:")
    print(f"1. Open http://localhost:8001 in your browser")
    print(f"2. Add a few deliveries using the form")
    print(f"3. Click 'Optimize Routes' to see the results")
    print(f"4. Check the detailed results page")
