#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Route Optimization Test
Tests the route optimization functionality without complex dependencies
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

class SimpleRouteOptimizer:
    """Simple route optimization implementation"""
    
    def __init__(self):
        self.data = None
        print("🚀 Simple Route Optimizer initialized!")
    
    def generate_sample_data(self, n_samples=100):
        """Generate sample delivery data"""
        print(f"📊 Generating {n_samples} sample deliveries...")
        
        # Nigerian delivery companies
        companies = ['GIG Logistics', 'Kwik Delivery', 'MAX.ng', 'Jumia Logistics', 'ACE Logistics']
        
        # Generate base data around Lagos
        base_lat, base_lon = 6.5244, 3.3792
        
        data = []
        for i in range(n_samples):
            record = {
                'order_id': f"ORD_{i:06d}",
                'company': np.random.choice(companies),
                'latitude': base_lat + np.random.normal(0, 0.05),
                'longitude': base_lon + np.random.normal(0, 0.05),
                'package_weight': np.random.lognormal(1.5, 0.8),
                'package_size': np.random.choice(['Small', 'Medium', 'Large'], p=[0.5, 0.35, 0.15]),
                'vehicle_type': np.random.choice(['Bike', 'Van', 'Truck'], p=[0.6, 0.3, 0.1]),
                'weather_condition': np.random.choice(['Clear', 'Rain', 'Cloudy'], p=[0.6, 0.2, 0.2]),
                'traffic_level': np.random.choice(['Low', 'Medium', 'High'], p=[0.3, 0.4, 0.3]),
                'hour': np.random.randint(6, 22),
                'day_of_week': np.random.randint(0, 7),
            }
            
            # Calculate distance from depot
            distance = np.sqrt((record['latitude'] - base_lat)**2 + 
                             (record['longitude'] - base_lon)**2) * 111
            record['distance_km'] = max(1, distance + np.random.normal(0, 2))
            
            # Calculate delivery time
            base_time = record['distance_km'] * 2.5
            if record['package_size'] == 'Large': base_time *= 1.3
            elif record['package_size'] == 'Medium': base_time *= 1.1
            if record['weather_condition'] == 'Rain': base_time *= 1.4
            if record['traffic_level'] == 'High': base_time *= 1.5
            elif record['traffic_level'] == 'Medium': base_time *= 1.2
            if record['hour'] in [8, 9, 17, 18, 19]: base_time *= 1.3
            
            record['delivery_time_minutes'] = max(10, base_time + np.random.normal(0, 5))
            
            # Calculate fuel cost
            if record['vehicle_type'] == 'Truck':
                fuel_cost = record['distance_km'] * np.random.uniform(3, 5)
            elif record['vehicle_type'] == 'Van':
                fuel_cost = record['distance_km'] * np.random.uniform(1.5, 3)
            else:  # Bike
                fuel_cost = record['distance_km'] * np.random.uniform(0.3, 1)
            
            record['fuel_cost'] = fuel_cost
            data.append(record)
        
        self.data = pd.DataFrame(data)
        print(f"✅ Generated {len(self.data)} delivery records")
        return self.data
    
    def simple_clustering(self, n_clusters=5):
        """Simple clustering based on location"""
        print(f"📍 Creating {n_clusters} geographical clusters...")
        
        coords = self.data[['latitude', 'longitude']].values
        n_points = len(coords)
        
        # Initialize cluster centers
        cluster_centers = []
        for i in range(n_clusters):
            idx = i * n_points // n_clusters
            cluster_centers.append(coords[idx])
        
        # Assign points to clusters
        clusters = []
        for coord in coords:
            distances = [np.sqrt((coord[0] - center[0])**2 + (coord[1] - center[1])**2) 
                       for center in cluster_centers]
            clusters.append(np.argmin(distances))
        
        self.data['cluster'] = clusters
        
        # Analyze clusters
        cluster_analysis = self.data.groupby('cluster').agg({
            'order_id': 'count',
            'delivery_time_minutes': 'mean',
            'distance_km': 'mean',
            'fuel_cost': 'mean',
            'company': 'nunique'
        }).round(2)
        
        print("📊 Cluster Analysis:")
        print(cluster_analysis)
        
        return cluster_analysis
    
    def solve_simple_tsp(self, cluster_id=0):
        """Solve TSP for a specific cluster"""
        print(f"🔄 Solving TSP for cluster {cluster_id}...")
        
        # Get deliveries in this cluster
        cluster_data = self.data[self.data['cluster'] == cluster_id]
        
        if len(cluster_data) <= 1:
            print(f"   Cluster {cluster_id} has only {len(cluster_data)} delivery(ies)")
            return []
        
        # Extract coordinates
        coords = cluster_data[['latitude', 'longitude']].values
        n = len(coords)
        
        # Calculate distance matrix
        distances = np.zeros((n, n))
        for i in range(n):
            for j in range(n):
                if i != j:
                    distances[i][j] = np.sqrt((coords[i][0] - coords[j][0])**2 + 
                                            (coords[i][1] - coords[j][1])**2) * 111
        
        # Nearest neighbor TSP
        unvisited = set(range(1, n))
        current = 0
        tour = [current]
        total_distance = 0
        
        while unvisited:
            nearest = min(unvisited, key=lambda x: distances[current][x])
            total_distance += distances[current][nearest]
            current = nearest
            tour.append(current)
            unvisited.remove(current)
        
        # Return to start
        total_distance += distances[current][0]
        tour.append(0)
        
        # Map back to original indices
        original_indices = cluster_data.index.tolist()
        optimized_route = [original_indices[i] for i in tour[:-1]]  # Exclude return to start
        
        print(f"   ✅ TSP solved for cluster {cluster_id}")
        print(f"   📏 Total distance: {total_distance:.2f} km")
        print(f"   🗺️ Route: {optimized_route}")
        
        return {
            'cluster_id': cluster_id,
            'route': optimized_route,
            'total_distance': total_distance,
            'deliveries_count': len(optimized_route)
        }
    
    def optimize_all_routes(self):
        """Optimize routes for all clusters"""
        print("\n🚀 Optimizing routes for all clusters...")
        
        if 'cluster' not in self.data.columns:
            self.simple_clustering()
        
        unique_clusters = self.data['cluster'].unique()
        optimized_routes = []
        
        total_distance = 0
        total_deliveries = 0
        
        for cluster_id in unique_clusters:
            route_result = self.solve_simple_tsp(cluster_id)
            if route_result:
                optimized_routes.append(route_result)
                total_distance += route_result['total_distance']
                total_deliveries += route_result['deliveries_count']
        
        # Calculate efficiency metrics
        individual_distance = 0
        for _, delivery in self.data.iterrows():
            # Round trip distance from depot
            depot_lat, depot_lon = 6.5244, 3.3792
            dist_to_delivery = np.sqrt((delivery['latitude'] - depot_lat)**2 + 
                                     (delivery['longitude'] - depot_lon)**2) * 111
            individual_distance += dist_to_delivery * 2  # Round trip
        
        distance_savings = individual_distance - total_distance
        savings_percentage = (distance_savings / individual_distance) * 100
        
        print(f"\n📊 OPTIMIZATION RESULTS:")
        print(f"=" * 40)
        print(f"🚚 Total routes: {len(optimized_routes)}")
        print(f"📦 Total deliveries: {total_deliveries}")
        print(f"📏 Optimized distance: {total_distance:.2f} km")
        print(f"📏 Individual trips distance: {individual_distance:.2f} km")
        print(f"💰 Distance savings: {distance_savings:.2f} km ({savings_percentage:.1f}%)")
        
        return {
            'routes': optimized_routes,
            'total_distance': total_distance,
            'individual_distance': individual_distance,
            'distance_savings': distance_savings,
            'savings_percentage': savings_percentage
        }
    
    def test_collaboration_opportunities(self):
        """Test multi-company collaboration opportunities"""
        print("\n🤝 Testing collaboration opportunities...")
        
        if 'cluster' not in self.data.columns:
            self.simple_clustering()
        
        # Analyze clusters for collaboration potential
        collaboration_data = self.data.groupby('cluster').agg({
            'company': 'nunique',
            'order_id': 'count',
            'fuel_cost': 'mean',
            'delivery_time_minutes': 'mean'
        })
        
        # Filter clusters with multiple companies
        collaboration_opportunities = collaboration_data[collaboration_data['company'] > 1]
        
        if len(collaboration_opportunities) > 0:
            print(f"✅ Found {len(collaboration_opportunities)} collaboration opportunities")
            print("📊 Collaboration clusters:")
            print(collaboration_opportunities)
            
            # Calculate potential savings
            total_potential_savings = 0
            for cluster_id, cluster_info in collaboration_opportunities.iterrows():
                cluster_deliveries = self.data[self.data['cluster'] == cluster_id]
                current_cost = cluster_deliveries['fuel_cost'].sum()
                potential_savings = current_cost * 0.20  # 20% savings through collaboration
                total_potential_savings += potential_savings
                
                print(f"   Cluster {cluster_id}: {cluster_info['company']} companies, "
                      f"₦{potential_savings:.2f} potential savings")
            
            print(f"💰 Total potential savings: ₦{total_potential_savings:.2f}")
        else:
            print("❌ No collaboration opportunities found")
        
        return collaboration_opportunities

def main():
    """Main test function"""
    print("🧪 SIMPLE ROUTE OPTIMIZATION TEST")
    print("=" * 50)
    
    # Initialize optimizer
    optimizer = SimpleRouteOptimizer()
    
    # Generate sample data
    data = optimizer.generate_sample_data(50)
    
    # Test clustering
    cluster_analysis = optimizer.simple_clustering(5)
    
    # Test route optimization
    optimization_results = optimizer.optimize_all_routes()
    
    # Test collaboration opportunities
    collaboration_results = optimizer.test_collaboration_opportunities()
    
    print(f"\n🎉 Route optimization test completed!")
    print(f"✅ All basic functionality working correctly")
    
    return optimizer, optimization_results

if __name__ == "__main__":
    optimizer, results = main()
    
    print(f"\n💡 The route optimization functionality is working!")
    print(f"🌐 You can now use:")
    print(f"   - Single delivery predictor: http://localhost:8000")
    print(f"   - Multi-delivery optimizer: http://localhost:8001")
    print(f"   - This simple optimizer for testing")
