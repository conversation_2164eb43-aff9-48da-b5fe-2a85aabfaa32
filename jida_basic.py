# -*- coding: utf-8 -*-
"""
JIDA PROJECT: Basic Last-Mile Delivery Optimization
Using only standard libraries for demonstration
"""

import pandas as pd
import numpy as np
import pickle
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

print("🚀 JIDA PROJECT: Basic Last-Mile Delivery Optimization")
print("=" * 60)
print("Using basic libraries for demonstration")
print("=" * 60)

class BasicLastMileOptimizer:
    """
    Basic Last-Mile Delivery Optimization System
    """

    def __init__(self):
        self.data = None
        self.model_data = {}
        print("🤖 Basic Last-Mile Optimizer initialized!")

    def generate_dummy_data(self, n_samples=1000):
        """Generate basic dummy delivery data"""
        print(f"\n📊 Generating {n_samples:,} delivery records...")
        np.random.seed(42)

        # Nigerian delivery companies
        companies = ['GIG Logistics', 'Kwik Delivery', 'MAX.ng', 'Jumia Logistics', 'ACE Logistics']
        
        # Generate base data
        data = []
        for i in range(n_samples):
            # Basic delivery record
            record = {
                'order_id': f"ORD_{i:06d}",
                'company': np.random.choice(companies),
                'latitude': 6.5244 + np.random.normal(0, 0.05),
                'longitude': 3.3792 + np.random.normal(0, 0.05),
                'package_weight': np.random.lognormal(1.5, 0.8),
                'package_size': np.random.choice(['Small', 'Medium', 'Large'], p=[0.5, 0.35, 0.15]),
                'vehicle_type': np.random.choice(['Bike', 'Van', 'Truck'], p=[0.6, 0.3, 0.1]),
                'weather_condition': np.random.choice(['Clear', 'Rain', 'Cloudy'], p=[0.6, 0.2, 0.2]),
                'traffic_level': np.random.choice(['Low', 'Medium', 'High'], p=[0.3, 0.4, 0.3]),
                'hour': np.random.randint(6, 22),
                'day_of_week': np.random.randint(0, 7),
            }
            
            # Calculate distance from depot
            depot_lat, depot_lon = 6.5244, 3.3792
            distance = np.sqrt((record['latitude'] - depot_lat)**2 + 
                             (record['longitude'] - depot_lon)**2) * 111
            record['distance_km'] = max(1, distance + np.random.normal(0, 2))
            
            # Calculate delivery time based on various factors
            base_time = record['distance_km'] * 2.5
            
            # Apply size factor
            if record['package_size'] == 'Large':
                base_time *= 1.3
            elif record['package_size'] == 'Medium':
                base_time *= 1.1
            
            # Apply weather factor
            if record['weather_condition'] == 'Rain':
                base_time *= 1.4
            elif record['weather_condition'] == 'Cloudy':
                base_time *= 1.1
            
            # Apply traffic factor
            if record['traffic_level'] == 'High':
                base_time *= 1.5
            elif record['traffic_level'] == 'Medium':
                base_time *= 1.2
            
            # Apply peak hour factor
            if record['hour'] in [8, 9, 17, 18, 19]:
                base_time *= 1.3
            
            # Apply vehicle factor
            if record['vehicle_type'] == 'Truck':
                base_time *= 1.2
            elif record['vehicle_type'] == 'Van':
                base_time *= 1.1
            
            record['delivery_time_minutes'] = max(10, base_time + np.random.normal(0, 5))
            
            # Calculate fuel cost
            if record['vehicle_type'] == 'Truck':
                fuel_cost = record['distance_km'] * np.random.uniform(3, 5)
            elif record['vehicle_type'] == 'Van':
                fuel_cost = record['distance_km'] * np.random.uniform(1.5, 3)
            else:  # Bike
                fuel_cost = record['distance_km'] * np.random.uniform(0.3, 1)
            
            record['fuel_cost'] = fuel_cost
            data.append(record)

        self.data = pd.DataFrame(data)
        print(f"✅ Generated {len(self.data):,} delivery records successfully!")
        return self.data

    def create_simple_model(self):
        """Create a simple prediction model using basic statistics"""
        print("\n🤖 Creating simple prediction model...")
        
        # Calculate average delivery times by different factors
        self.model_data = {
            'avg_by_size': self.data.groupby('package_size')['delivery_time_minutes'].mean().to_dict(),
            'avg_by_weather': self.data.groupby('weather_condition')['delivery_time_minutes'].mean().to_dict(),
            'avg_by_traffic': self.data.groupby('traffic_level')['delivery_time_minutes'].mean().to_dict(),
            'avg_by_vehicle': self.data.groupby('vehicle_type')['delivery_time_minutes'].mean().to_dict(),
            'avg_by_hour': self.data.groupby('hour')['delivery_time_minutes'].mean().to_dict(),
            'distance_factor': self.data['delivery_time_minutes'].sum() / self.data['distance_km'].sum(),
            'base_time': self.data['delivery_time_minutes'].mean()
        }
        
        print("✅ Simple model created successfully!")
        return self.model_data

    def predict_delivery_time(self, distance, package_size, weather, traffic, vehicle, hour):
        """Predict delivery time using the simple model"""
        if not self.model_data:
            self.create_simple_model()
        
        # Start with distance-based time
        predicted_time = distance * self.model_data['distance_factor']
        
        # Apply factors based on model data
        size_factor = self.model_data['avg_by_size'].get(package_size, self.model_data['base_time']) / self.model_data['base_time']
        weather_factor = self.model_data['avg_by_weather'].get(weather, self.model_data['base_time']) / self.model_data['base_time']
        traffic_factor = self.model_data['avg_by_traffic'].get(traffic, self.model_data['base_time']) / self.model_data['base_time']
        vehicle_factor = self.model_data['avg_by_vehicle'].get(vehicle, self.model_data['base_time']) / self.model_data['base_time']
        hour_factor = self.model_data['avg_by_hour'].get(hour, self.model_data['base_time']) / self.model_data['base_time']
        
        # Apply all factors
        predicted_time *= size_factor * weather_factor * traffic_factor * vehicle_factor * hour_factor
        
        return max(10, predicted_time)

    def export_model(self):
        """Export the model data as pickle file"""
        print("\n💾 Exporting model...")
        
        # Create models directory
        os.makedirs('models', exist_ok=True)
        
        # Save model data
        model_path = 'models/basic_delivery_model.pkl'
        with open(model_path, 'wb') as f:
            pickle.dump({
                'model_data': self.model_data,
                'data_sample': self.data.head(100).to_dict('records')  # Save sample for reference
            }, f)
        
        print(f"✅ Model exported to {model_path}")
        return model_path

    def get_statistics(self):
        """Get basic statistics about the data"""
        if self.data is None:
            return {}
        
        stats = {
            'total_deliveries': len(self.data),
            'avg_delivery_time': self.data['delivery_time_minutes'].mean(),
            'avg_distance': self.data['distance_km'].mean(),
            'avg_fuel_cost': self.data['fuel_cost'].mean(),
            'company_distribution': self.data['company'].value_counts().to_dict(),
            'vehicle_distribution': self.data['vehicle_type'].value_counts().to_dict(),
            'size_distribution': self.data['package_size'].value_counts().to_dict()
        }
        return stats

def main():
    # Initialize optimizer
    optimizer = BasicLastMileOptimizer()
    
    # Generate data
    data = optimizer.generate_dummy_data(1000)
    
    # Create simple model
    model_data = optimizer.create_simple_model()
    
    # Export model
    model_path = optimizer.export_model()
    
    # Test prediction
    print("\n🧪 Testing prediction...")
    test_prediction = optimizer.predict_delivery_time(
        distance=5.0,
        package_size='Medium',
        weather='Clear',
        traffic='Medium',
        vehicle='Van',
        hour=14
    )
    print(f"Test prediction: {test_prediction:.2f} minutes")
    
    # Show statistics
    stats = optimizer.get_statistics()
    print(f"\n📊 Statistics:")
    print(f"Total deliveries: {stats['total_deliveries']}")
    print(f"Average delivery time: {stats['avg_delivery_time']:.2f} minutes")
    print(f"Average distance: {stats['avg_distance']:.2f} km")
    print(f"Average fuel cost: ₦{stats['avg_fuel_cost']:.2f}")
    
    print("\n🎉 Basic model training and export completed!")
    print(f"Model saved at: {model_path}")
    
    return optimizer

if __name__ == "__main__":
    optimizer = main()
