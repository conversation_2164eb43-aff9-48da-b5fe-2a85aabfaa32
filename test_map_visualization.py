#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to set up deliveries for map visualization testing
"""

import requests
import json
import time

def setup_test_deliveries():
    """Set up test deliveries for map visualization"""
    base_url = "http://localhost:8001"
    
    print("🗺️ Setting up deliveries for map visualization test")
    print("=" * 60)
    
    # Clear any existing deliveries first
    print("1. Clearing existing deliveries...")
    try:
        response = requests.post(f"{base_url}/clear_deliveries")
        if response.status_code == 200:
            print("   ✅ Existing deliveries cleared")
        else:
            print(f"   ⚠️ Clear request returned: {response.status_code}")
    except Exception as e:
        print(f"   ⚠️ Clear request failed: {e}")
    
    # Test deliveries spread across Lagos
    test_deliveries = [
        {
            'id': 'MAP_001',
            'lat': 6.5244,  # Lagos Island
            'lon': 3.3792,
            'package_weight': 5.2,
            'package_size': 'Medium',
            'priority': 'High',
            'time_window_start': '09:00',
            'time_window_end': '17:00'
        },
        {
            'id': 'MAP_002',
            'lat': 6.4698,  # Ikeja
            'lon': 3.3378,
            'package_weight': 2.1,
            'package_size': 'Small',
            'priority': 'Medium',
            'time_window_start': '10:00',
            'time_window_end': '18:00'
        },
        {
            'id': 'MAP_003',
            'lat': 6.5795,  # Victoria Island
            'lon': 3.3211,
            'package_weight': 8.7,
            'package_size': 'Large',
            'priority': 'High',
            'time_window_start': '08:00',
            'time_window_end': '16:00'
        },
        {
            'id': 'MAP_004',
            'lat': 6.4281,  # Surulere
            'lon': 3.3611,
            'package_weight': 3.4,
            'package_size': 'Medium',
            'priority': 'Low',
            'time_window_start': '11:00',
            'time_window_end': '19:00'
        },
        {
            'id': 'MAP_005',
            'lat': 6.6018,  # Ikoyi
            'lon': 3.3515,
            'package_weight': 1.8,
            'package_size': 'Small',
            'priority': 'Medium',
            'time_window_start': '09:30',
            'time_window_end': '17:30'
        },
        {
            'id': 'MAP_006',
            'lat': 6.4474,  # Yaba
            'lon': 3.3903,
            'package_weight': 6.3,
            'package_size': 'Medium',
            'priority': 'High',
            'time_window_start': '08:30',
            'time_window_end': '16:30'
        },
        {
            'id': 'MAP_007',
            'lat': 6.5344,  # Lagos Mainland
            'lon': 3.3892,
            'package_weight': 4.1,
            'package_size': 'Medium',
            'priority': 'Medium',
            'time_window_start': '10:30',
            'time_window_end': '18:30'
        },
        {
            'id': 'MAP_008',
            'lat': 6.4969,  # Apapa
            'lon': 3.3560,
            'package_weight': 12.5,
            'package_size': 'Large',
            'priority': 'High',
            'time_window_start': '07:00',
            'time_window_end': '15:00'
        },
        {
            'id': 'MAP_009',
            'lat': 6.5144,  # Lekki Phase 1
            'lon': 3.4744,
            'package_weight': 3.8,
            'package_size': 'Medium',
            'priority': 'Medium',
            'time_window_start': '09:00',
            'time_window_end': '17:00'
        },
        {
            'id': 'MAP_010',
            'lat': 6.4644,  # Gbagada
            'lon': 3.3844,
            'package_weight': 7.2,
            'package_size': 'Large',
            'priority': 'Low',
            'time_window_start': '12:00',
            'time_window_end': '20:00'
        }
    ]
    
    print(f"\n2. Adding {len(test_deliveries)} test deliveries...")
    
    for i, delivery in enumerate(test_deliveries, 1):
        try:
            response = requests.post(
                f"{base_url}/add_delivery",
                headers={'Content-Type': 'application/json'},
                data=json.dumps(delivery)
            )
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"   ✅ Added {delivery['id']} - {delivery['package_size']} package, {delivery['priority']} priority")
                else:
                    print(f"   ❌ Failed to add {delivery['id']}: {result.get('error', 'unknown error')}")
                    return False
            else:
                print(f"   ❌ HTTP error adding {delivery['id']}: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error adding {delivery['id']}: {e}")
            return False
    
    print(f"\n3. Optimizing routes...")
    try:
        response = requests.post(f"{base_url}/optimize_routes")
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                results = result['results']
                routes = result['routes']
                
                print(f"   ✅ Optimization successful!")
                print(f"   🚚 Routes created: {results['total_routes']}")
                print(f"   📏 Total distance: {results['total_distance_km']} km")
                print(f"   ⏱️ Total time: {results['total_time_minutes']:.1f} minutes")
                print(f"   💰 Total cost: ₦{results['total_cost_naira']:.2f}")
                
                print(f"\n   📋 Route breakdown:")
                for i, route in enumerate(routes, 1):
                    print(f"      Route {i}: {route['vehicle_type']} - {route['total_deliveries']} deliveries")
                    delivery_ids = [d['delivery_id'] for d in route['deliveries']]
                    print(f"         Sequence: {' → '.join(delivery_ids)}")
                
                return True
            else:
                print(f"   ❌ Optimization failed: {result.get('error', 'unknown error')}")
                return False
        else:
            print(f"   ❌ HTTP error during optimization: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Optimization error: {e}")
        return False

def test_map_endpoints():
    """Test the map-related endpoints"""
    base_url = "http://localhost:8001"
    
    print(f"\n4. Testing map endpoints...")
    
    # Test status endpoint
    try:
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            status = response.json()
            print(f"   ✅ Status endpoint: {status['total_deliveries']} deliveries, {status['total_routes']} routes")
        else:
            print(f"   ❌ Status endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Status endpoint error: {e}")
    
    # Test map endpoint
    try:
        response = requests.get(f"{base_url}/map")
        if response.status_code == 200:
            print(f"   ✅ Map endpoint accessible (returned {len(response.content)} bytes)")
        elif response.status_code == 302:
            print(f"   ⚠️ Map endpoint redirected (no routes available)")
        else:
            print(f"   ❌ Map endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Map endpoint error: {e}")
    
    # Test results endpoint
    try:
        response = requests.get(f"{base_url}/results")
        if response.status_code == 200:
            print(f"   ✅ Results endpoint accessible (returned {len(response.content)} bytes)")
        elif response.status_code == 302:
            print(f"   ⚠️ Results endpoint redirected (no routes available)")
        else:
            print(f"   ❌ Results endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Results endpoint error: {e}")

if __name__ == "__main__":
    print("🗺️ JIDA Map Visualization Test Setup")
    print("=" * 60)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    try:
        success = setup_test_deliveries()
        
        if success:
            test_map_endpoints()
            
            print(f"\n🎉 MAP VISUALIZATION SETUP COMPLETE!")
            print(f"=" * 50)
            print(f"🌐 You can now test the map visualization:")
            print(f"")
            print(f"📍 Main Interface:")
            print(f"   http://localhost:8001")
            print(f"")
            print(f"🗺️ Map Visualization:")
            print(f"   http://localhost:8001/map")
            print(f"")
            print(f"📊 Detailed Results:")
            print(f"   http://localhost:8001/results")
            print(f"")
            print(f"✨ Features to test:")
            print(f"   • Interactive map with route lines")
            print(f"   • Color-coded routes for different vehicles")
            print(f"   • Clickable markers with delivery details")
            print(f"   • Route toggle controls")
            print(f"   • Depot and delivery point visualization")
            print(f"   • Responsive design for mobile/desktop")
            
        else:
            print(f"\n❌ SETUP FAILED!")
            print(f"⚠️ There were issues setting up the test deliveries.")
            
    except Exception as e:
        print(f"\n💥 SETUP ERROR: {e}")
        print(f"🔍 Make sure the server is running on port 8001")
    
    print(f"\n📋 Manual Steps:")
    print(f"1. Open http://localhost:8001 in your browser")
    print(f"2. Add some deliveries if needed")
    print(f"3. Click 'Optimize Routes'")
    print(f"4. Click 'View Map' to see the visualization")
    print(f"5. Explore the interactive features!")
