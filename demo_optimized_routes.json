{"optimization_results": {"total_routes": 2, "total_deliveries": 8, "total_distance_km": 47.64, "total_time_minutes": 152.93, "total_cost_naira": 23.82, "average_deliveries_per_route": 4.0, "optimization_timestamp": "2025-08-01T08:06:55.980599"}, "routes": [{"route_id": "ROUTE_001", "vehicle_type": "Bike", "total_deliveries": 6, "total_distance_km": 25.44, "total_time_minutes": 102.65, "total_cost_naira": 12.72, "total_weight_kg": 33.6, "deliveries": [{"delivery_id": "DEL_001", "sequence": 1, "distance_from_previous": 0.0, "estimated_time": 10, "package_weight": 5.2, "priority": "High"}, {"delivery_id": "DEL_006", "sequence": 2, "distance_from_previous": 8.635350566711226, "estimated_time": 31.689867517867086, "package_weight": 6.3, "priority": "High"}, {"delivery_id": "DEL_004", "sequence": 3, "distance_from_previous": 3.885206137388346, "estimated_time": 14.257865598192113, "package_weight": 3.4, "priority": "Low"}, {"delivery_id": "DEL_002", "sequence": 4, "distance_from_previous": 5.302245880756612, "estimated_time": 17.0367767649289, "package_weight": 2.1, "priority": "Medium"}, {"delivery_id": "DEL_008", "sequence": 5, "distance_from_previous": 3.623516751720603, "estimated_time": 15.013876010815897, "package_weight": 12.5, "priority": "High"}, {"delivery_id": "DEL_007", "sequence": 6, "distance_from_previous": 3.9936714048604403, "estimated_time": 14.655909653257009, "package_weight": 4.1, "priority": "Medium"}], "efficiency_score": 5.84}, {"route_id": "ROUTE_002", "vehicle_type": "Bike", "total_deliveries": 2, "total_distance_km": 22.2, "total_time_minutes": 50.27, "total_cost_naira": 11.1, "total_weight_kg": 10.5, "deliveries": [{"delivery_id": "DEL_003", "sequence": 1, "distance_from_previous": 8.888057719209556, "estimated_time": 36.82726083985187, "package_weight": 8.7, "priority": "High"}, {"delivery_id": "DEL_005", "sequence": 2, "distance_from_previous": 4.184935537137926, "estimated_time": 13.446719395001486, "package_weight": 1.8, "priority": "Medium"}], "efficiency_score": 3.98}], "original_deliveries": [{"id": "DEL_001", "lat": 6.5244, "lon": 3.3792, "package_weight": 5.2, "package_size": "Medium", "priority": "High", "time_window_start": "09:00", "time_window_end": "17:00", "weather": "Clear", "traffic": "Medium", "vehicle_preference": "Any", "special_instructions": ""}, {"id": "DEL_002", "lat": 6.4698, "lon": 3.3378, "package_weight": 2.1, "package_size": "Small", "priority": "Medium", "time_window_start": "10:00", "time_window_end": "18:00", "weather": "Clear", "traffic": "Medium", "vehicle_preference": "Any", "special_instructions": ""}, {"id": "DEL_003", "lat": 6.5795, "lon": 3.3211, "package_weight": 8.7, "package_size": "Large", "priority": "High", "time_window_start": "08:00", "time_window_end": "16:00", "weather": "Clear", "traffic": "Medium", "vehicle_preference": "Any", "special_instructions": ""}, {"id": "DEL_004", "lat": 6.4281, "lon": 3.3611, "package_weight": 3.4, "package_size": "Medium", "priority": "Low", "time_window_start": "11:00", "time_window_end": "19:00", "weather": "Clear", "traffic": "Medium", "vehicle_preference": "Any", "special_instructions": ""}, {"id": "DEL_005", "lat": 6.6018, "lon": 3.3515, "package_weight": 1.8, "package_size": "Small", "priority": "Medium", "time_window_start": "09:30", "time_window_end": "17:30", "weather": "Clear", "traffic": "Medium", "vehicle_preference": "Any", "special_instructions": ""}, {"id": "DEL_006", "lat": 6.4474, "lon": 3.3903, "package_weight": 6.3, "package_size": "Medium", "priority": "High", "time_window_start": "08:30", "time_window_end": "16:30", "weather": "Clear", "traffic": "Medium", "vehicle_preference": "Any", "special_instructions": ""}, {"id": "DEL_007", "lat": 6.5244, "lon": 3.3792, "package_weight": 4.1, "package_size": "Medium", "priority": "Medium", "time_window_start": "10:30", "time_window_end": "18:30", "weather": "Clear", "traffic": "Medium", "vehicle_preference": "Any", "special_instructions": ""}, {"id": "DEL_008", "lat": 6.4969, "lon": 3.356, "package_weight": 12.5, "package_size": "Large", "priority": "High", "time_window_start": "07:00", "time_window_end": "15:00", "weather": "Clear", "traffic": "Medium", "vehicle_preference": "Any", "special_instructions": ""}]}